package ai.guiji.duix.test.ui.activity

import ai.guiji.duix.sdk.client.BuildConfig
import ai.guiji.duix.test.R
import ai.guiji.duix.test.databinding.ActivityMainBinding
import ai.guiji.duix.test.service.StorageService
import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.text.Editable
import android.text.TextWatcher
import android.widget.SeekBar
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import java.io.BufferedReader
import java.io.File
import java.io.FileInputStream
import java.io.InputStreamReader

class MainActivity : BaseActivity() {

    companion object {
        const val PERMISSION_REQUEST_CODE = 1001
        const val PREF_NAME = "duix_settings"
        const val KEY_TTS_URL = "tts_url"
        const val KEY_APIKEY = "apikey"
        const val DEFAULT_TTS_URL = ""
        const val REFERENCE_ID = "reference_id"

        // 语音检测参数相关常量
        const val KEY_AMPLITUDE_THRESHOLD = "amplitude_threshold"
        const val KEY_VOICE_DETECTION_THRESHOLD = "voice_detection_threshold"
        const val KEY_SILENCE_DETECTION_MS = "silence_detection_ms"
        const val KEY_VALID_FRAME_RATIO = "valid_frame_ratio"
        
        const val DEFAULT_AMPLITUDE_THRESHOLD = 129
        const val DEFAULT_VOICE_DETECTION_THRESHOLD = 2
        const val DEFAULT_SILENCE_DETECTION_MS = 1300
        const val DEFAULT_VALID_FRAME_RATIO = 4

        const val man_key = "man"
        const val woman_key = "woman"
    }

    private lateinit var binding: ActivityMainBinding
    private val sharedPrefs by lazy { getSharedPreferences(PREF_NAME, MODE_PRIVATE) }

    private val baseConfigUrl = "https://cdn.guiji.ai/duix/location/gj_dh_res.zip"
    private lateinit var baseDir: File
    private val baseConfigUUID = "d39caddd-488b-4682-b6d1-13549b135dd1"     // 可以用来控制模型文件版本
    private var baseConfigReady = false

    // https://cdn.guiji.ai/duix/digital/model/1706009711636/liangwei_540s.zip
    // https://cdn.guiji.ai/duix/digital/model/1706009766199/mingzhi_540s.zip
    private var modelUrl = "" //        "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719193748558/airuike_20240409.zip"   // ** 在这里更新模型地址 ** //        "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719194036608/zixuan_20240411v2.zip"   // ** 在这里更新模型地址 ** //        "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719193425133/sufei_20240409.zip"   // ** 在这里更新模型地址 **
    //        "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719193451931/lengyan_20240407.zip"   // ** 在这里更新模型地址 **

    // private val modelUrl_male = "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719193748558/airuike_20240409.zip"
    private val modelUrl_male = "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719194633518/mingxuan_20240624.zip"
//    private val modelUrl_female = "https://digital-public.obs.cn-east-3.myhuaweicloud.com/duix/digital/model/1719193451931/lengyan_20240407.zip"
    private val modelUrl_female = "https://digital-public.obs.cn-east-3.myhuaweicloud.com/dhp-tools/dhp-tools/651637733658693/61000/651637733658693_2e0a4278a73411a2ff04ef1a849d2a6d.zip"

    // 声音检测参数
    private var amplitudeThreshold = DEFAULT_AMPLITUDE_THRESHOLD
    private var voiceDetectionThreshold = DEFAULT_VOICE_DETECTION_THRESHOLD
    private var silenceDetectionMs = DEFAULT_SILENCE_DETECTION_MS
    private var validFrameRatio = DEFAULT_VALID_FRAME_RATIO

    init {
        modelUrl = modelUrl_male
    }

    private lateinit var modelDir: File
    private val liangweiUUID = "d39caddd-488b-4682-b6d1-13549b135dd1"       // 可以用来控制模型文件版本
    private var modelReady = false

    // 添加需要检查的权限列表
    private val requiredPermissions = arrayOf(Manifest.permission.RECORD_AUDIO, Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE)

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        binding.tvSdkVersion.text = "SDK Version: ${BuildConfig.VERSION_NAME}"

        // 从SharedPreferences加载语音检测参数
        loadVoiceDetectionParams()
        
        // 初始化滑块控件
        initVoiceDetectionSliders()

        var reference_id = if(binding.rbMale.isChecked) man_key else woman_key
        sharedPrefs.edit().putString(REFERENCE_ID, reference_id).apply()
        // 设置性别选择的监听器
        binding.rgVoiceGender.setOnCheckedChangeListener { _, checkedId ->
            var reference_id = ""
            modelUrl = when (checkedId) {
                R.id.rb_male -> {
                    reference_id = man_key
                    modelUrl_male
                }

                R.id.rb_female -> {
                    reference_id = woman_key
                    modelUrl_female
                }

                else -> modelUrl_male
            }
            sharedPrefs.edit().putString(REFERENCE_ID, reference_id).apply()
            // 更新模型目录
            val duixDir = mContext.getExternalFilesDir("duix")
            modelDir = File(duixDir, modelUrl.substring(modelUrl.lastIndexOf("/") + 1)
                .replace(".zip", ""))
            downloadModel() // 重置模型下载状态
        }

        val duixDir = mContext.getExternalFilesDir("duix")
        if (!duixDir!!.exists()) {
            duixDir.mkdirs()
        }
        baseDir = File(duixDir, baseConfigUrl.substring(baseConfigUrl.lastIndexOf("/") + 1)
            .replace(".zip", ""))
        modelDir = File(duixDir, modelUrl.substring(modelUrl.lastIndexOf("/") + 1)
            .replace(".zip", ""))        // 这里要求存放模型的文件夹的名字和下载的zip文件的一致以对应解压的文件夹路径

        // 从 SharedPreferences 加载上次保存的 URL
        val savedUrl = sharedPrefs.getString(KEY_TTS_URL, DEFAULT_TTS_URL)
        binding.etTtsUrl.setText(savedUrl)

        // 从 SharedPreferences 加载上次保存的 API Key
        val savedApiKey = sharedPrefs.getString(KEY_APIKEY, "")
        binding.etApikey.setText(savedApiKey)

        binding.btnBaseConfigDownload.setOnClickListener {
            downloadBaseConfig()
        }
        binding.btnModelPlay.setOnClickListener {
            if (!modelReady) {
                downloadModel()
            } else if (!baseConfigReady) {
                Toast.makeText(mContext, "您必须正确安装基础配置文件", Toast.LENGTH_SHORT).show()
            } else if (checkPermissions()) { // 获取并验证当前输入的 URL
                val ttsUrl = binding.etTtsUrl.text.toString().trim()
                if (ttsUrl.isEmpty()) {
                    Toast.makeText(mContext, "请输入TTS服务地址", Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }

                // 保存当前输入的 URL
                sharedPrefs.edit().putString(KEY_TTS_URL, ttsUrl).apply()

                startCallActivity(ttsUrl)
            } else {
                requestPermissions()
            }
        }

        // 添加 URL 输入框的文本变化监听
        binding.etTtsUrl.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) { // 当用户输入完成后自动保存
                s?.toString()?.trim()?.let { url ->
                    if (url.isNotEmpty()) {
                        sharedPrefs.edit().putString(KEY_TTS_URL, url).apply()
                    }
                }
            }
        })

        // 添加 API Key 输入框的文本变化监听
        binding.etApikey.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) { // 当用户输入完成后自动保存
                s?.toString()?.trim()?.let { apiKey ->
                    sharedPrefs.edit().putString(KEY_APIKEY, apiKey).apply()
                }
            }
        })

        checkFile()
    }

    private fun checkPermissions(): Boolean {
        return requiredPermissions.all { permission ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && permission in arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE)) { // Android 10 及以上不需要存储权限
                true
            } else {
                ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
            }
        }
    }

    private fun requestPermissions() {
        val permissionsToRequest = requiredPermissions.filter { permission ->
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && permission in arrayOf(Manifest.permission.WRITE_EXTERNAL_STORAGE, Manifest.permission.READ_EXTERNAL_STORAGE)) { // Android 10 及以上不需要存储权限
                false
            } else {
                ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED
            }
        }.toTypedArray()

        if (permissionsToRequest.isNotEmpty()) {
            ActivityCompat.requestPermissions(this, permissionsToRequest, PERMISSION_REQUEST_CODE)
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int,
                                            permissions: Array<out String>,
                                            grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == PERMISSION_REQUEST_CODE) {
            if (grantResults.isNotEmpty() && grantResults.all { it == PackageManager.PERMISSION_GRANTED }) {
                if (!modelReady) {
                    downloadModel()
                } else if (!baseConfigReady) {
                    Toast.makeText(mContext, "您必须正确安装基础配置文件", Toast.LENGTH_SHORT)
                        .show()
                } else { // 获取并验证当前输入的 URL
                    val ttsUrl = binding.etTtsUrl.text.toString().trim()
                    if (ttsUrl.isNotEmpty()) {
                        startCallActivity(ttsUrl)
                    } else {
                        Toast.makeText(mContext, "请输入TTS服务地址", Toast.LENGTH_SHORT).show()
                    }
                }
            } else {
                Toast.makeText(this, "需要录音和存储权限才能使用该功能", Toast.LENGTH_SHORT).show()
                showPermissionExplanationDialog()
            }
        }
    }

    private fun showPermissionExplanationDialog() {
        AlertDialog.Builder(this).setTitle("需要权限")
            .setMessage("此功能需要录音和存储权限才能正常使用。请在设置中开启相关权限。")
            .setPositiveButton("去设置") { _, _ -> // 跳转到应用设置页面
                openAppSettings()
            }.setNegativeButton("取消", null).show()
    }

    private fun openAppSettings() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.fromParts("package", packageName, null)
        }
        startActivity(intent)
    }

    private fun startCallActivity(ttsUrl: String? = null) {
        val intent = Intent(this, CallActivity::class.java).apply {
            putExtra("baseDir", baseDir.absolutePath)
            putExtra("modelDir", modelDir.absolutePath)
            putExtra("ttsUrl", ttsUrl)  // 添加 ttsUrl 参数
            putExtra("apiKey", binding.etApikey.text?.toString()?.trim())  // 添加 apiKey 参数
            
            // 添加语音检测参数
            putExtra(KEY_AMPLITUDE_THRESHOLD, amplitudeThreshold)
            putExtra(KEY_VOICE_DETECTION_THRESHOLD, voiceDetectionThreshold)
            putExtra(KEY_SILENCE_DETECTION_MS, silenceDetectionMs)
            putExtra(KEY_VALID_FRAME_RATIO, validFrameRatio)
        }
        startActivity(intent)
    }

    private fun downloadBaseConfig() {
        binding.btnBaseConfigDownload.isEnabled = false
        binding.progressBaseConfig.progress = 0
        StorageService.downloadAndUnzip(mContext, baseConfigUrl, baseDir.absolutePath, baseConfigUUID, object :
            StorageService.Callback {
            override fun onDownloadProgress(progress: Int) {
                runOnUiThread {
                    binding.progressBaseConfig.progress = progress / 2
                }
            }

            override fun onUnzipProgress(progress: Int) {
                runOnUiThread {
                    binding.progressBaseConfig.progress = 50 + progress / 2
                }
            }

            override fun onComplete(path: String?) {
                runOnUiThread {
                    binding.btnBaseConfigDownload.isEnabled = false
                    binding.btnBaseConfigDownload.text = getString(R.string.ready)
                    binding.progressBaseConfig.progress = 100
                    baseConfigReady = true
                }
            }

            override fun onError(msg: String?) {
                runOnUiThread {
                    binding.btnBaseConfigDownload.isEnabled = true
                    binding.progressBaseConfig.progress = 0
                    Toast.makeText(mContext, "文件下载异常: $msg", Toast.LENGTH_SHORT).show()
                }
            }
        }, true)
    }

    private fun downloadModel() { // 检查模型文件是否存在且完整
        if (modelDir.exists() && File(modelDir, "/uuid").exists() && liangweiUUID == BufferedReader(InputStreamReader(FileInputStream(File(modelDir, "/uuid")))).readLine()) {
            binding.btnModelPlay.isEnabled = true
            binding.btnModelPlay.text = "进入数字人"
            binding.progressModel.progress = 100
            modelReady = true
            return
        }

        // 如果文件不存在或不完整，开始下载
        binding.btnModelPlay.isEnabled = false
        binding.btnModelPlay.text = getString(R.string.download)
        binding.progressModel.progress = 0
        StorageService.downloadAndUnzip(mContext, modelUrl, modelDir.absolutePath, liangweiUUID, object :
            StorageService.Callback {
            override fun onDownloadProgress(progress: Int) {
                runOnUiThread {
                    binding.progressModel.progress = progress / 2
                }
            }

            override fun onUnzipProgress(progress: Int) {
                runOnUiThread {
                    binding.progressModel.progress = 50 + progress / 2;
                }
            }

            override fun onComplete(path: String?) {
                runOnUiThread {
                    binding.btnModelPlay.isEnabled = true
                    binding.btnModelPlay.text = "进入数字人"
                    binding.progressModel.progress = 100
                    modelReady = true
                }
            }

            override fun onError(msg: String?) {
                runOnUiThread {
                    binding.btnModelPlay.isEnabled = true
                    binding.btnModelPlay.text = getString(R.string.download)
                    binding.progressModel.progress = 0
                    Toast.makeText(mContext, "文件下载异常: $msg", Toast.LENGTH_SHORT).show()
                }
            }
        }, false        // for debug
        )
    }

    private fun checkFile() {
        if (baseDir.exists() && File(baseDir, "/uuid").exists() && baseConfigUUID == BufferedReader(InputStreamReader(FileInputStream(File(baseDir, "/uuid")))).readLine()) {
            binding.btnBaseConfigDownload.isEnabled = false
            binding.btnBaseConfigDownload.text = getString(R.string.ready)
            binding.progressBaseConfig.progress = 100
            baseConfigReady = true
        } else {
            binding.btnBaseConfigDownload.isEnabled = true
            binding.progressBaseConfig.progress = 0
        }
        if (modelDir.exists() && File(modelDir, "/uuid").exists() && liangweiUUID == BufferedReader(InputStreamReader(FileInputStream(File(modelDir, "/uuid")))).readLine()) {
            binding.btnModelPlay.isEnabled = true
            binding.btnModelPlay.text = "进入数字人"
            binding.progressModel.progress = 100
            modelReady = true
        } else {
            binding.btnModelPlay.isEnabled = true
            binding.btnModelPlay.text = getString(R.string.download)
            binding.progressModel.progress = 0
        }
    }

    private fun loadVoiceDetectionParams() {
        amplitudeThreshold = sharedPrefs.getInt(KEY_AMPLITUDE_THRESHOLD, DEFAULT_AMPLITUDE_THRESHOLD)
        voiceDetectionThreshold = sharedPrefs.getInt(KEY_VOICE_DETECTION_THRESHOLD, DEFAULT_VOICE_DETECTION_THRESHOLD)
        silenceDetectionMs = sharedPrefs.getInt(KEY_SILENCE_DETECTION_MS, DEFAULT_SILENCE_DETECTION_MS)
        validFrameRatio = sharedPrefs.getInt(KEY_VALID_FRAME_RATIO, DEFAULT_VALID_FRAME_RATIO)
    }

    private fun initVoiceDetectionSliders() {
        // 从SharedPreferences加载已保存的值或使用默认值
        val prefs = getSharedPreferences(PREF_NAME, MODE_PRIVATE)
        val savedAmplitudeThreshold = prefs.getInt(KEY_AMPLITUDE_THRESHOLD, DEFAULT_AMPLITUDE_THRESHOLD)
        val savedVoiceDetectionThreshold = prefs.getInt(KEY_VOICE_DETECTION_THRESHOLD, DEFAULT_VOICE_DETECTION_THRESHOLD)
        val savedSilenceDetectionMs = prefs.getInt(KEY_SILENCE_DETECTION_MS, DEFAULT_SILENCE_DETECTION_MS)
        val savedValidFrameRatio = prefs.getInt(KEY_VALID_FRAME_RATIO, DEFAULT_VALID_FRAME_RATIO)
        
        // 设置振幅阈值滑块
        binding.tvAmplitudeThresholdValue.text = savedAmplitudeThreshold.toString()
        binding.seekBarAmplitudeThreshold.progress = savedAmplitudeThreshold
        binding.seekBarAmplitudeThreshold.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                binding.tvAmplitudeThresholdValue.text = progress.toString()
                // 保存到SharedPreferences
                prefs.edit().putInt(KEY_AMPLITUDE_THRESHOLD, progress).apply()
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
        
        // 设置语音检测阈值滑块
        binding.tvVoiceDetectionThresholdValue.text = savedVoiceDetectionThreshold.toString()
        binding.seekBarVoiceDetectionThreshold.progress = savedVoiceDetectionThreshold
        binding.seekBarVoiceDetectionThreshold.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                binding.tvVoiceDetectionThresholdValue.text = progress.toString()
                // 保存到SharedPreferences
                prefs.edit().putInt(KEY_VOICE_DETECTION_THRESHOLD, progress).apply()
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
        
        // 设置静音检测时长滑块
        binding.tvSilenceDetectionMsValue.text = "${savedSilenceDetectionMs}ms"
        binding.seekBarSilenceDetectionMs.progress = savedSilenceDetectionMs / 100 // 转换为滑块范围
        binding.seekBarSilenceDetectionMs.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                val actualValue = progress * 100 // 转换回毫秒
                binding.tvSilenceDetectionMsValue.text = "${actualValue}ms"
                // 保存到SharedPreferences
                prefs.edit().putInt(KEY_SILENCE_DETECTION_MS, actualValue).apply()
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
        
        // 设置有效帧比例滑块
        binding.tvValidFrameRatioValue.text = "$savedValidFrameRatio%"
        binding.seekBarValidFrameRatio.progress = savedValidFrameRatio
        binding.seekBarValidFrameRatio.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                binding.tvValidFrameRatioValue.text = "$progress%"
                // 保存到SharedPreferences
                prefs.edit().putInt(KEY_VALID_FRAME_RATIO, progress).apply()
            }
            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
        
        // 重置按钮
        binding.btnResetVoiceParams.setOnClickListener {
            // 重置到默认值
            binding.seekBarAmplitudeThreshold.progress = DEFAULT_AMPLITUDE_THRESHOLD
            binding.seekBarVoiceDetectionThreshold.progress = DEFAULT_VOICE_DETECTION_THRESHOLD
            binding.seekBarSilenceDetectionMs.progress = DEFAULT_SILENCE_DETECTION_MS / 100
            binding.seekBarValidFrameRatio.progress = DEFAULT_VALID_FRAME_RATIO
            
            // 保存默认值到SharedPreferences
            prefs.edit()
                .putInt(KEY_AMPLITUDE_THRESHOLD, DEFAULT_AMPLITUDE_THRESHOLD)
                .putInt(KEY_VOICE_DETECTION_THRESHOLD, DEFAULT_VOICE_DETECTION_THRESHOLD)
                .putInt(KEY_SILENCE_DETECTION_MS, DEFAULT_SILENCE_DETECTION_MS)
                .putInt(KEY_VALID_FRAME_RATIO, DEFAULT_VALID_FRAME_RATIO)
                .apply()
            
            Toast.makeText(this, "已重置语音检测参数", Toast.LENGTH_SHORT).show()
        }
    }
}
