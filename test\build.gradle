plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

android {
    namespace 'ai.guiji.duix.test'
    compileSdk 34

    defaultConfig {
        applicationId "ai.guiji.duix.test"
        minSdk 24
        targetSdk 34
        versionCode 1
        versionName "3.0.0"
    }

    signingConfigs {
        release {
            storeFile file('../demo.jks')
            storePassword '123456'
            keyAlias 'demo'
            keyPassword '123456'
        }
    }

    //日志开关
    def LOG_DEBUG = "LOG_DEBUG"
    //FileProvider
    def AUTHORITY = "AUTHORITY"

    defaultConfig {
        buildConfigField "boolean", LOG_DEBUG, "true"
        buildConfigField "String", AUTHORITY, "\"${defaultConfig.applicationId}.fileprovider\""
    }

    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        viewBinding true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.3'
    }
    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }

    lint {
        baseline = file("lint-baseline.xml")
    }
    lintOptions{
        checkReleaseBuilds false
    }
}

dependencies {

    implementation fileTree(include: ['*.jar', '*.aar'], dir: 'libs')
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.2.0'
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.1'
    implementation "androidx.activity:activity:1.3.0"
    implementation "androidx.fragment:fragment:1.3.0"

    implementation 'com.github.bumptech.glide:glide:4.12.0'

    implementation project(":duix-sdk")
    implementation project(":SherpaOnnxKws")
    implementation project(":minerva")
//    implementation project(":sherp")
    implementation 'com.squareup.okhttp3:okhttp:4.10.0'
    implementation 'com.squareup.okhttp3:okhttp-sse:4.10.0'
    implementation 'com.google.android.exoplayer:exoplayer:2.14.2'

    implementation 'ink.lodz:pandora:2.0.8'
    implementation "com.github.permissions-dispatcher:ktx:1.1.4"
    implementation 'com.github.wendykierp:JTransforms:3.1'
    implementation 'androidx.annotation:annotation:1.8.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.0'
}