<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/iv_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <ai.guiji.duix.sdk.client.render.DUIXTextureView
        android:id="@+id/glTextureView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!-- 对话内容容器 - 半透明黑色背景 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cv_dialog_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        app:cardBackgroundColor="#80000000"
        app:cardCornerRadius="16dp"
        app:cardElevation="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/suggestion_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintWidth_percent="0.9"
        android:layout_marginBottom="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 问题显示区域 - 右侧 -->
            <LinearLayout
                android:id="@+id/ll_question_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="end"
                android:layout_marginBottom="16dp">

                <TextView
                    android:id="@+id/tv_question_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="问题："
                    android:textColor="#4CAF50"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:layout_marginEnd="8dp" />

                <TextView
                    android:id="@+id/tv_question"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_question_bubble"
                    android:padding="12dp"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:maxWidth="320dp"
                    android:minWidth="120dp"
                    android:textAlignment="viewEnd" />
            </LinearLayout>

            <!-- 回答显示区域 - 左侧 -->
            <LinearLayout
                android:id="@+id/ll_answer_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="start">

                <TextView
                    android:id="@+id/tv_answer_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="回答："
                    android:textColor="#2196F3"
                    android:textSize="15sp"
                    android:textStyle="bold"
                    android:layout_marginEnd="8dp" />

                <ScrollView
                    android:id="@+id/scrollView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:maxHeight="200dp">

                    <TextView
                        android:id="@+id/tv_answer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:background="@drawable/bg_answer_bubble"
                        android:padding="12dp"
                        android:textColor="#FFFFFF"
                        android:textSize="16sp"
                        android:minWidth="120dp"
                        android:maxWidth="320dp" />
                </ScrollView>
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 建议问题容器 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/suggestion_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="20dp"
        android:layout_marginEnd="20dp"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="#80000000"
        app:cardCornerRadius="16dp"
        app:cardElevation="0dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/mic_button_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintWidth_percent="0.9">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="猜您想问："
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <!-- 建议问题1 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/suggestion_card_1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardElevation="0dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                app:cardCornerRadius="10dp">
                
                <TextView
                    android:id="@+id/suggestion_1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_suggestion_item"
                    android:padding="12dp"
                    android:textColor="#FFFFFF"
                    android:textSize="15sp"
                    android:drawableEnd="@android:drawable/ic_media_play"
                    android:drawableTint="#FFFFFF"
                    android:drawablePadding="8dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground"/>
            </androidx.cardview.widget.CardView>

            <!-- 建议问题2 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/suggestion_card_2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardElevation="0dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                app:cardCornerRadius="10dp">
                
                <TextView
                    android:id="@+id/suggestion_2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_suggestion_item"
                    android:padding="12dp"
                    android:textColor="#FFFFFF"
                    android:textSize="15sp"
                    android:drawableEnd="@android:drawable/ic_media_play"
                    android:drawableTint="#FFFFFF"
                    android:drawablePadding="8dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground"/>
            </androidx.cardview.widget.CardView>

            <!-- 建议问题3 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/suggestion_card_3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="@android:color/transparent"
                app:cardElevation="0dp"
                android:clickable="true"
                android:focusable="true"
                android:foreground="?attr/selectableItemBackground"
                app:cardCornerRadius="10dp">
                
                <TextView
                    android:id="@+id/suggestion_3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/bg_suggestion_item"
                    android:padding="12dp"
                    android:textColor="#FFFFFF"
                    android:textSize="15sp"
                    android:drawableEnd="@android:drawable/ic_media_play"
                    android:drawableTint="#FFFFFF"
                    android:drawablePadding="8dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackground"/>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_voice_type"
        android:visibility="gone"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:hint="选择音色"
        app:layout_constraintBottom_toTopOf="@id/btnPlay"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <AutoCompleteTextView
            android:id="@+id/act_voice_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="none"
            android:textSize="14sp"
            android:padding="12dp"/>

    </com.google.android.material.textfield.TextInputLayout>

    <Button
        android:id="@+id/btnPlay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="12dp"
        android:text="播放音频"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/mic_button_container"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- 麦克风按钮容器 - 确保按钮位置在屏幕中部偏下 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/mic_button_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.85">

        <!-- 录音按钮 - 美化样式 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/mic_button_card"
            android:layout_width="72dp"
            android:layout_height="72dp"
            app:cardCornerRadius="36dp"
            app:cardElevation="8dp"
            app:cardBackgroundColor="#2196F3"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:background="@drawable/mic_button_selector">

                <ImageView
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:src="@android:drawable/ic_btn_speak_now"
                    android:tint="#FFFFFF"
                    android:contentDescription="语音按钮"/>
            </LinearLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnRecord"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                android:background="@android:color/transparent"
                app:iconGravity="textStart"
                app:iconPadding="0dp"
                app:iconSize="0dp"
                app:cornerRadius="36dp"
                android:text=""
                android:textSize="0sp"
                app:backgroundTint="@android:color/transparent" />
        </androidx.cardview.widget.CardView>

        <!-- 状态文本 - 显示在按钮下方 -->
        <TextView
            android:id="@+id/recording_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="等待语音..."
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold"
            android:background="#80000000"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingTop="6dp"
            android:paddingBottom="6dp"
            android:layout_marginTop="12dp"
            app:layout_constraintTop_toBottomOf="@id/mic_button_card"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:elevation="4dp"
            android:gravity="center"
            android:minWidth="120dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>