<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission
        android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.BROADCAST_STICKY" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />

    <!-- Used to set ringtone -->
    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <!--电池优化权限-->
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <!--Android 13通知权限-->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
    <!--Android 13 读取音频权限-->
    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.WRITE_MEDIA_VIDEO/" />

    <uses-permission android:name="android.permission.CAMERA" />

    <application
        android:name=".App"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:usesCleartextTraffic="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.DUIX.Test">
        <activity
            android:name=".ui.activity.MainActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <activity
            android:name=".ui.activity.CallActivity"
            android:exported="false"></activity>

        <activity
            android:name="com.k2fsa.sherpa.ncnn.demo.SpeechRecognizerDemo"
            android:exported="false"
            android:label="语音识别示例"></activity>


        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_provider_paths" />
        </provider>

        <activity
            android:name="com.lodz.android.minervademo.ui.MainVoiceActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity">

        </activity>

        <activity
            android:name="com.lodz.android.minervademo.ui.simple.SimpleActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity">

        </activity>

        <activity
            android:name="com.lodz.android.minervademo.ui.vad.VadActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            tools:ignore="LockedOrientationActivity">

        </activity>

    </application>

</manifest>