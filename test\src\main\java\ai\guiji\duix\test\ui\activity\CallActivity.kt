package ai.guiji.duix.test.ui.activity

import ai.guiji.duix.sdk.client.Constant
import ai.guiji.duix.sdk.client.DUIX
import ai.guiji.duix.sdk.client.render.DUIXRenderer
import ai.guiji.duix.test.databinding.ActivityCallBinding
import ai.guiji.duix.test.util.LogUtils
import ai.guiji.duix.test.utils.DeviceIdUtil
import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import android.opengl.GLSurfaceView
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import android.widget.ArrayAdapter
import android.widget.ScrollView
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.bumptech.glide.Glide
import com.fasterxml.jackson.databind.ObjectMapper
import com.lodz.android.minerva.agent.MinervaAgent
import com.lodz.android.minerva.bean.AudioFormats
import com.lodz.android.minerva.bean.states.Finish
import com.lodz.android.minerva.bean.states.Recording
import com.lodz.android.minerva.contract.Minerva
import com.lodz.android.minerva.contract.OnRecordingStatesListener
import okhttp3.Call
import okhttp3.Callback
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.Response
import okhttp3.sse.EventSource
import okhttp3.sse.EventSourceListener
import okhttp3.sse.EventSources
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

class CallActivity : BaseActivity() {

    companion object {
        const val GL_CONTEXT_VERSION = 2
        private const val DEFAULT_TTS_URL = "http://************:8280/v1/tts"
        private const val PERMISSION_REQUEST_CODE = 1001
        private const val TAG_NET = "DUIX_NET"
    }

    private var baseDir = ""
    private var modelDir = ""
    private var ttsUrl = DEFAULT_TTS_URL
    private var apiKey = ""
    private var voiceType = "man"
    
    // 语音检测参数
    private var amplitudeThreshold = 129
    private var voiceDetectionThreshold = 2
    private var silenceDetectionMs = 1300
    private var validFrameRatio = 4

    // 添加对话状态跟踪
    private var hasConversationStarted = false

    private var binding: ActivityCallBinding? = null
    private var duix: DUIX? = null
    private var mDUIXRender: DUIXRenderer? = null
    private val mFilePath by lazy { getExternalFilesDir("vad")?.absolutePath ?: "" }
    private var isProcessingRequest = false
    private val objectMapper = ObjectMapper()
    private var isPlaying = false
    private val sharedPrefs by lazy { getSharedPreferences(MainActivity.PREF_NAME, MODE_PRIVATE) }

    var apikey: String? = null
    var reference_id: String? = null

    // 添加SSE和打断相关变量
    private var currentEventSource: EventSource? = null
    private val audioChunks = mutableListOf<File>()
    private var currentChunkIndex = 0
    private var isInterrupted = false

    // 添加音频相关变量
    private var audioRecord: AudioRecord? = null
    private var isRecordingAudio = false
    private val audioBufferSize = 1024
    private val audioThread = Executors.newSingleThreadExecutor()
    private var lastVoiceDetectedTime = 0L
    private var lastRequestTime = 0L
    private val requestCooldownTime = 3000L // 请求冷却时间：3秒

    // 创建一个配置了超时时间的 OkHttpClient
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .retryOnConnectionFailure(true)
        .build()

    // 添加Minerva录音控制器
    private var minerva: Minerva? = null
    private var isRecording = false
    
    // 添加语音检测相关变量
    private var isVoiceDetected = false
    private var voiceStartTime = 0L
    private var lastVoiceTime = 0L
    private val minVoiceDuration = 1000L // 最小语音持续时间（毫秒）
    private val silenceThreshold = 1500L // 静音判定阈值（毫秒）
    private var isProcessingVoice = false
    private var voiceDetector: SherpaOnnxKws? = null // 添加语音检测器变量

    // 添加文本显示相关变量
    private var questionText: String? = null
    private var answerText: String? = null
    private var textHideHandler = Handler(Looper.getMainLooper())
    private val TEXT_DISPLAY_DURATION = 12000L // 延长文本显示时间到12秒
    
    // 流式显示相关变量
    private val typingHandler = Handler(Looper.getMainLooper())
    private var currentTypingPosition = 0
    private var fullAnswerText = ""
    private val TYPING_DELAY = 15L // 稍微加快打字速度

    // 添加语音文本同步变量
    private val audioTextMap = HashMap<File, String>() // 存储音频文件和对应的文本
    private val questionMap = HashMap<File, String>() // 存储音频文件对应的问题
    
    // 添加建议问题相关变量
    private val suggestedQuestions = mutableListOf<String>()
    private var currentSuggestedQuestions = mutableListOf<String>()

    // 添加一个标志变量来跟踪是否收到了有效回答
    private var hasValidResponse = false
    
    // 添加文本处理的标志
    private var shouldPreserveTextOnInterrupt = true // 默认在打断时保留文本一段时间
    private var isTextPreserved = false // 标记文本是否正在被保留显示

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        keepScreenOn()
        binding = ActivityCallBinding.inflate(layoutInflater)
        setContentView(binding?.root)
        
        // 添加全局异常处理器，防止崩溃
        Thread.setDefaultUncaughtExceptionHandler { thread, throwable ->
            Log.e(TAG_NET, "未捕获的异常: ${throwable.message}", throwable)
            try {
                // 尝试记录日志
                LogUtils.getInstance(this).log("未捕获的异常: ${throwable.message}")
            } catch (e: Exception) {
                // 忽略日志记录失败
            }
            // 将异常传递给默认处理器
            Thread.getDefaultUncaughtExceptionHandler()?.uncaughtException(thread, throwable)
        }

        baseDir = intent.getStringExtra("baseDir") ?: ""
        modelDir = intent.getStringExtra("modelDir") ?: ""
        ttsUrl = intent.getStringExtra("ttsUrl") ?: DEFAULT_TTS_URL
        apiKey = intent.getStringExtra("apiKey") ?: ""
        voiceType = intent.getStringExtra("voiceType") ?: "man"
        
        // 获取语音检测参数
        amplitudeThreshold = intent.getIntExtra(MainActivity.KEY_AMPLITUDE_THRESHOLD, 129)
        voiceDetectionThreshold = intent.getIntExtra(MainActivity.KEY_VOICE_DETECTION_THRESHOLD, 2)
        silenceDetectionMs = intent.getIntExtra(MainActivity.KEY_SILENCE_DETECTION_MS, 1300)
        validFrameRatio = intent.getIntExtra(MainActivity.KEY_VALID_FRAME_RATIO, 4)
        
        Log.i(TAG_NET, "语音检测参数: 振幅阈值=$amplitudeThreshold, 检测阈值=$voiceDetectionThreshold, " +
                "静音时长=${silenceDetectionMs}ms, 有效帧比例=$validFrameRatio%")

        // 设置音色选择下拉框
        val voiceTypes = arrayOf("女声", "男声")
        val adapter = ArrayAdapter(this, com.google.android.material.R.layout.support_simple_spinner_dropdown_item, voiceTypes)
        binding?.actVoiceType?.setAdapter(adapter)

        // 设置初始值
        binding?.actVoiceType?.setText(if (reference_id == MainActivity.woman_key) "女声" else "男声", false)

        // 添加音色选择监听
        binding?.actVoiceType?.setOnItemClickListener { _, _, position, _ ->
            // 根据选择设置reference_id
            reference_id = if (position == 0) MainActivity.woman_key else MainActivity.man_key
            // 存储到SharedPreferences
            sharedPrefs.edit().putString(MainActivity.REFERENCE_ID, reference_id).apply()
            Log.i(TAG_NET, "音色已切换为: $reference_id")
        }

        binding?.btnPlay?.setOnClickListener {
            playWav()
        }

        // 添加建议问题CardView的点击事件
        binding?.suggestionCard1?.setOnClickListener {
            Log.i(TAG_NET, "建议问题卡片1被点击")
            if (currentSuggestedQuestions.isNotEmpty() && currentSuggestedQuestions.size >= 1) {
                askSuggestedQuestion(currentSuggestedQuestions[0])
            }
        }
        
        binding?.suggestionCard2?.setOnClickListener {
            Log.i(TAG_NET, "建议问题卡片2被点击")
            if (currentSuggestedQuestions.isNotEmpty() && currentSuggestedQuestions.size >= 2) {
                askSuggestedQuestion(currentSuggestedQuestions[1])
            }
        }
        
        binding?.suggestionCard3?.setOnClickListener {
            Log.i(TAG_NET, "建议问题卡片3被点击")
            if (currentSuggestedQuestions.isNotEmpty() && currentSuggestedQuestions.size >= 3) {
                askSuggestedQuestion(currentSuggestedQuestions[2])
            }
        }

        // 保留原有的TextView点击事件作为备用
        binding?.suggestion1?.setOnClickListener {
            Log.i(TAG_NET, "建议问题1被点击")
            if (currentSuggestedQuestions.isNotEmpty() && currentSuggestedQuestions.size >= 1) {
                askSuggestedQuestion(currentSuggestedQuestions[0])
            }
        }
        
        binding?.suggestion2?.setOnClickListener {
            Log.i(TAG_NET, "建议问题2被点击")
            if (currentSuggestedQuestions.isNotEmpty() && currentSuggestedQuestions.size >= 2) {
                askSuggestedQuestion(currentSuggestedQuestions[1])
            }
        }
        
        binding?.suggestion3?.setOnClickListener {
            Log.i(TAG_NET, "建议问题3被点击")
            if (currentSuggestedQuestions.isNotEmpty() && currentSuggestedQuestions.size >= 3) {
                askSuggestedQuestion(currentSuggestedQuestions[2])
            }
        }

        binding?.ivBg?.let { imageView ->
            Glide.with(mContext).load("file:///android_asset/bg/hospital.png").into(imageView)
        }

        binding?.glTextureView?.setEGLContextClientVersion(GL_CONTEXT_VERSION)
        binding?.glTextureView?.setEGLConfigChooser(8, 8, 8, 8, 16, 0) // 透明
        binding?.glTextureView?.isOpaque = false           // 透明

        mDUIXRender = DUIXRenderer(mContext, binding?.glTextureView)
        binding?.glTextureView?.setRenderer(mDUIXRender)
        binding?.glTextureView?.renderMode = GLSurfaceView.RENDERMODE_WHEN_DIRTY      // 一定要在设置完Render之后再调用

        duix = DUIX(mContext, baseDir, modelDir, mDUIXRender) { event, msg, info ->
            when (event) {
                Constant.CALLBACK_EVENT_INIT_READY -> {
                    initOk()
                }

                Constant.CALLBACK_EVENT_INIT_ERROR -> {
                    runOnUiThread {
                        // 移除Toast，保留日志记录
                        Log.e(TAG_NET, "初始化异常: $msg")
                    }
                }

                Constant.CALLBACK_EVENT_AUDIO_PLAY_START -> {
                    Log.i(TAG_NET, "数字人开始播放")
                    isPlaying = true
                    hasValidResponse = true // 设置标志，表示已收到有效回答
                    
                    // 确保录音状态被重置，避免状态混乱
                    if (isRecording) {
                        isRecording = false
                        minerva?.stop()
                    }
                    
                    runOnUiThread {
                        binding?.btnRecord?.isEnabled = true // 允许打断
                        updateUIState("点击打断", true, "音频开始播放，更新UI为打断状态")
                    }
                    
                    // 显示当前播放片段对应的文本(问题和回答同时显示)
                    if (currentChunkIndex < audioChunks.size) {
                        val currentAudio = audioChunks[currentChunkIndex]
                        // 获取对应的问题和回答
                        val questionForAudio = questionMap[currentAudio]
                        val textForAudio = audioTextMap[currentAudio]
                        
                        Log.i(TAG_NET, "准备显示文本 - 问题: ${questionForAudio?.take(20)}..., 回答: ${textForAudio?.take(20)}...")
                        
                        // 确保同时显示问题和回答
                        if (!questionForAudio.isNullOrEmpty() && !textForAudio.isNullOrEmpty()) {
                            Log.i(TAG_NET, "显示问题和回答")
                            showDialogWithTexts(questionForAudio, textForAudio)
                        } else if (!textForAudio.isNullOrEmpty()) {
                            // 如果没有问题但有回答，使用全局问题文本
                            if (!questionText.isNullOrEmpty()) {
                                Log.i(TAG_NET, "使用全局问题文本和当前回答")
                                showDialogWithTexts(questionText!!, textForAudio)
                            } else {
                                // 如果实在没有问题，只显示回答
                                Log.i(TAG_NET, "没有问题，只显示回答")
                                showDialogWithAnswer(textForAudio)
                            }
                        } else {
                            Log.w(TAG_NET, "没有文本可显示")
                        }
                    } else {
                        Log.w(TAG_NET, "没有可播放的音频片段")
                    }
                }

                Constant.CALLBACK_EVENT_AUDIO_PLAY_END -> {
                    Log.i(TAG_NET, "数字人播放结束")
                    
                    if (isInterrupted) {
                        Log.i(TAG_NET, "播放已被打断，不继续播放下一段")
                        // 如果被打断，隐藏问答文本
                        hideQuestionAnswerText()
                        return@DUIX
                    }
                    
                    // 检查是否有下一段要播放
                    val nextIndex = currentChunkIndex + 1
                    Log.i(TAG_NET, "当前播放索引: $currentChunkIndex, 下一个索引: $nextIndex, 总片段数: ${audioChunks.size}")
                    
                    if (nextIndex < audioChunks.size) {
                        // 播放下一段
                        Log.i(TAG_NET, "继续播放下一段音频")
                        playAudioChunk(audioChunks[nextIndex])
                    } else {
                        // 全部播放完毕
                        Log.i(TAG_NET, "所有音频片段播放完毕，立即开始录音")
                        isPlaying = false
                        isProcessingRequest = false
                        
                        // 在播放完毕几秒后隐藏文本
                        scheduleHideText()
                        
                        // 如果有建议问题，显示它们
                        if (currentSuggestedQuestions.isNotEmpty()) {
                            showSuggestedQuestions()
                            
                            // 添加：延迟隐藏建议问题
                            try {
                                // 延迟30秒后，如果仍未检测到有效语音，才隐藏建议问题
                                Handler(Looper.getMainLooper()).postDelayed({
                                    // 仅当当前没有播放且没有处理请求时才隐藏
                                    if (!isPlaying && !isProcessingRequest && !isFinishing && !isDestroyed) {
                                        Log.i(TAG_NET, "30秒内未检测到语音，隐藏建议问题")
                                        hideSuggestedQuestions()
                                    }
                                }, 30000) // 30秒延迟
                            } catch (e: Exception) {
                                Log.e(TAG_NET, "设置延迟隐藏建议问题失败", e)
                            }
                        }
                        
                        // 重置语音检测状态
                        isVoiceDetected = false
                        isProcessingVoice = false
                        
                        // 立即开始录音
                        runOnUiThread {
                            binding?.btnRecord?.isEnabled = true
                            updateUIState("录音中...", true, "所有音频播放完毕，立即开始录音")
                        }
                        
                        // 确保录音立即开始
                        if (!isRecording) {
                            isRecording = true
                            minerva?.start()
                        }
                        
                        // 重新启动语音检测
                        try {
                            voiceDetector?.stopListening()
                            voiceDetector?.startListening(object : AudioStreamObserver {
                                override fun onVoiceStart() {
                                    Log.i(TAG_NET, "检测到语音开始")
                                }

                                override fun onVoiceEnd(silenceDuration: Long) {
                                    Log.i(TAG_NET, "检测到语音结束，持续时间：$silenceDuration ms")
                                    if (isRecording && !isPlaying && !isProcessingRequest) {
                                        isRecording = false
                                        minerva?.stop()
                                    }
                                }

                                override fun onError(e: Exception) {
                                    Log.e(TAG_NET, "语音检测错误", e)
                                }
                            })
                        } catch (e: Exception) {
                            Log.e(TAG_NET, "重启语音检测失败", e)
                        }
                    }
                }

                Constant.CALLBACK_EVENT_AUDIO_PLAY_ERROR -> {
                    Log.e(TAG_NET, "数字人播放错误: $msg")
                    isPlaying = false
                    isProcessingRequest = false
                    
                    // 重置语音检测状态
                    isVoiceDetected = false
                    isProcessingVoice = false
                    
                    // 立即开始录音
                    runOnUiThread {
                        binding?.btnRecord?.isEnabled = true
                        binding?.recordingStatus?.text = "录音中..."
                    }
                    
                    // 确保录音立即开始
                    if (!isRecording) {
                        isRecording = true
                        minerva?.start()
                    }
                    
                    // 重新启动语音检测
                    try {
                        voiceDetector?.stopListening()
                        voiceDetector?.startListening(object : AudioStreamObserver {
                            override fun onVoiceStart() {
                                Log.i(TAG_NET, "检测到语音开始")
                            }

                            override fun onVoiceEnd(silenceDuration: Long) {
                                Log.i(TAG_NET, "检测到语音结束，持续时间：$silenceDuration ms")
                                if (isRecording && !isPlaying && !isProcessingRequest) {
                                    isRecording = false
                                    minerva?.stop()
                                }
                            }

                            override fun onError(e: Exception) {
                                Log.e(TAG_NET, "语音检测错误", e)
                            }
                        })
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "重启语音检测失败", e)
                    }
                }

                Constant.CALLBACK_EVENT_AUDIO_PLAY_PROGRESS -> { //                    Log.e(TAG, "audio play progress: $info")

                }
            }
        } // 异步回调结果
        duix?.init()

        binding?.btnRecord?.isEnabled = false  // 初始时按钮不可点击
        updateUIState("初始化中...", false, "显示初始化状态")
        
        // 修正：设置整个麦克风按钮卡片的点击事件，确保点击任何地方都能触发打断
        binding?.micButtonCard?.setOnClickListener { 
            try {
                Log.i(TAG_NET, "麦克风按钮被点击")
                if (isPlaying) {
                    Log.i(TAG_NET, "正在播放中，执行打断")
                    // 使用新的线程执行打断，避免UI线程阻塞
                    Thread {
                        try {
                            interruptPlayback()
                        } catch (e: Exception) {
                            Log.e(TAG_NET, "新线程中执行打断失败", e)
                        }
                    }.start()
                } else {
                    Log.i(TAG_NET, "当前不在播放状态，点击无效")
                }
            } catch (e: Exception) {
                // 捕获所有异常，防止崩溃
                Log.e(TAG_NET, "麦克风按钮点击处理异常", e)
            }
        }
        
        // 同时保留原来透明按钮的点击事件，确保覆盖整个区域
        binding?.btnRecord?.setOnClickListener { 
            try {
                Log.i(TAG_NET, "录音按钮被点击")
                if (isPlaying) {
                    Log.i(TAG_NET, "正在播放中，执行打断")
                    interruptPlayback() 
                } else {
                    Log.i(TAG_NET, "当前不在播放状态，点击无效")
                }
            } catch (e: Exception) {
                // 捕获所有异常，防止崩溃
                Log.e(TAG_NET, "录音按钮点击处理异常", e)
            }
        }
        
        // 初始化其他变量
        apikey = sharedPrefs.getString(MainActivity.KEY_APIKEY, "")
        reference_id = sharedPrefs.getString(MainActivity.REFERENCE_ID, "")
        // 如果reference_id为空，则设置默认值
        if (reference_id.isNullOrEmpty()) {
            reference_id = MainActivity.man_key // 默认为男声
        }
        Log.i(TAG_NET, "当前音色: $reference_id")
        
        // 检查录音权限，但不要在这里初始化，等待initOk回调中处理
        if (!checkRecordPermission()) {
            requestRecordPermission()
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.i(TAG_NET, "界面销毁，释放资源")
        
        // 停止语音检测
        try {
            voiceDetector?.stopListening()
            voiceDetector = null
        } catch (e: Exception) {
            Log.e(TAG_NET, "停止语音检测出错", e)
        }
        
        // 取消SSE连接
        try {
            currentEventSource?.cancel()
            currentEventSource = null
        } catch (e: Exception) {
            Log.e(TAG_NET, "取消SSE连接出错", e)
        }
        
        // 停止录音
        try {
            if (isRecording) {
                minerva?.stop()
            }
            minerva = null
        } catch (e: Exception) {
            Log.e(TAG_NET, "停止录音出错", e)
        }
        
        try {
            duix?.release()
            mDUIXRender?.release()
        } catch (e: Exception) {
            Log.e(TAG_NET, "释放DUIX资源出错", e)
        }

        // 清理资源
        audioChunks.clear()
        audioTextMap.clear()
        questionMap.clear() // 清理问题映射
        stopAudioCapture()
        audioThread.shutdown()

        // 移除所有待处理的文本隐藏请求和打字效果
        textHideHandler.removeCallbacksAndMessages(null)
        typingHandler.removeCallbacksAndMessages(null)
    }

    // 语音检测初始化
    private fun initVoiceDetection() {
        // 如果已经初始化，则不再初始化
        if (voiceDetector != null) {
            Log.i(TAG_NET, "语音检测器已存在，不再重复初始化")
            return
        }
        
        Log.i(TAG_NET, "初始化语音检测器，参数: 振幅阈值=$amplitudeThreshold, 检测阈值=$voiceDetectionThreshold, " +
                "静音时长=${silenceDetectionMs}ms, 有效帧比例=$validFrameRatio%")
        
        // 使用SherpaOnnxKws进行语音活动检测，传入用户设置的参数
        voiceDetector = SherpaOnnxKws(
            modelConfig = ModelConfig(
                encoder = "sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01/encoder-epoch-99-avg-1-chunk-16-left-64.onnx",
                decoder = "sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01/decoder-epoch-99-avg-1-chunk-16-left-64.onnx",
                joiner = "sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01/joiner-epoch-99-avg-1-chunk-16-left-64.onnx",
                tokens = "sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01/tokens.txt",
                keywords = "sherpa-onnx-kws-zipformer-wenetspeech-3.3M-2024-01-01/keywords.txt"
            ),
            sampleRate = 16000,
            numThreads = 2,
            amplitudeThreshold = amplitudeThreshold,
            voiceDetectionThreshold = voiceDetectionThreshold,
            silenceDetectionMs = silenceDetectionMs
        )

        // 创建音频流监听
        val audioStreamObserver = object : AudioStreamObserver {
            override fun onVoiceStart() {
                Log.i(TAG_NET, "检测到语音开始, 当前状态: isRecording=$isRecording, isPlaying=$isPlaying, isProcessingRequest=$isProcessingRequest")
                // 检测到语音开始，仅在非处理状态和非播放状态时开始录音
                if (!isProcessingRequest && !isPlaying && !isRecording) {
                    Log.i(TAG_NET, "开始录音")
                    isRecording = true
                    runOnUiThread {
                        binding?.btnRecord?.isEnabled = true
                        binding?.recordingStatus?.text = "录音中..."
                    }
                    // 启动录音
                    minerva?.start()
                } else {
                    Log.d(TAG_NET, "已在处理中或播放中，忽略语音开始信号")
                }
            }

            override fun onVoiceEnd(silenceDuration: Long) {
                Log.i(TAG_NET, "检测到语音结束，持续时间：$silenceDuration ms, 当前状态: isRecording=$isRecording, isPlaying=$isPlaying, isProcessingRequest=$isProcessingRequest")
                // 检测到语音结束，仅在录音状态且非播放/处理状态时处理
                if (isRecording && !isPlaying && !isProcessingRequest) {
                    Log.i(TAG_NET, "停止录音")
                    isRecording = false
                    
                    // 设置UI为处理中状态
                    runOnUiThread {
                        binding?.btnRecord?.isEnabled = false
                        binding?.recordingStatus?.text = "处理中..."
                    }
                    
                    // 几乎立即停止录音
                    Log.i(TAG_NET, "停止录音")
                    minerva?.stop() // 这将触发录音完成回调，在那里会检查音频有效性
                } else {
                    if (isPlaying) {
                        Log.d(TAG_NET, "当前正在播放中，忽略静音结束信号")
                    } else if (isProcessingRequest) {
                        Log.d(TAG_NET, "当前正在处理请求中，忽略静音结束信号")
                    } else if (!isRecording) {
                        Log.d(TAG_NET, "当前未在录音状态，忽略静音结束信号")
                        
                        // 确保在非播放和非处理状态下，自动开始录音，显示"录音中..."
                        if (!isPlaying && !isProcessingRequest) {
                            runOnUiThread {
                                binding?.btnRecord?.isEnabled = true
                                binding?.recordingStatus?.text = "录音中..."
                            }
                            
                            // 启动录音
                            isRecording = true
                            minerva?.start()
                            Log.i(TAG_NET, "自动开始录音")
                        }
                    }
                }
            }

            override fun onError(e: Exception) {
                Log.e(TAG_NET, "语音检测错误", e)
                runOnUiThread {
                    // 移除Toast，保留状态重置
                    resetRecordingState()
                }
            }
        }

        // 启动语音检测
        try {
            if (voiceDetector != null) {
                voiceDetector?.startListening(audioStreamObserver)
                Log.i(TAG_NET, "语音检测器启动完成，振幅阈值=${amplitudeThreshold}，语音检测阈值=${voiceDetectionThreshold}，静音检测时间=${silenceDetectionMs}ms，有效帧比例=${validFrameRatio}%")
                
                // 更新UI状态为等待语音
                runOnUiThread {
                    updateUIState("等待语音...", false, "语音检测启动完成，等待语音输入")
                }
            } else {
                Log.w(TAG_NET, "语音检测器为null，无法启动")
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "语音检测器启动失败", e)
            voiceDetector = null
        }
    }

    // 打断当前播放
    private fun interruptPlayback() {
        try {
            // 首先检查当前线程是否是主线程，如果不是则切换到主线程
            if (Looper.myLooper() != Looper.getMainLooper()) {
                Log.i(TAG_NET, "不在主线程，切换到主线程执行打断")
                runOnUiThread {
                    try {
                        safeInterruptPlayback()
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "主线程中执行打断失败", e)
                        // 即使失败也不会崩溃
                    }
                }
                return
            }
            
            // 已经在主线程中，直接执行
            safeInterruptPlayback()
        } catch (e: Exception) {
            // 捕获所有可能的异常，确保不会崩溃
            Log.e(TAG_NET, "打断播放过程中发生未预期的异常", e)
            try {
                // 尝试最小化恢复
                if (!isFinishing && !isDestroyed) {
                    isPlaying = false
                    isProcessingRequest = false
                    isInterrupted = true
                    // 移除Toast
                    Log.e(TAG_NET, "打断过程中出现异常")
                }
            } catch (e2: Exception) {
                Log.e(TAG_NET, "恢复状态失败", e2)
            }
        }
    }
    
    // 安全的打断实现，包含完整的异常处理
    private fun safeInterruptPlayback() {
        // 确保在主线程中执行整个打断过程
        try {
            // 仅在播放状态时允许打断
            if (!isPlaying) {
                Log.i(TAG_NET, "当前不在播放状态，无需打断")
                return
            }
            
            Log.i(TAG_NET, "用户打断当前播放")
            isInterrupted = true
            
            // 使用try-catch分别处理每个步骤，确保即使一个步骤失败也能继续执行其他步骤
            
            // 1. 停止当前音频播放 - 使用强制方式
            try {
                Log.i(TAG_NET, "强制停止当前音频播放")
                if (duix != null) {
                    try {
                        duix?.stopAudio()
                        Log.i(TAG_NET, "DUIX停止音频成功")
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "DUIX停止音频出错", e)
                    }
                } else {
                    Log.w(TAG_NET, "DUIX实例为null，无法停止音频")
                }
                
                // 手动处理播放结束逻辑
                Log.i(TAG_NET, "手动处理播放结束逻辑")
                // 设置为结束状态，强制停止后续播放
                currentChunkIndex = audioChunks.size
            } catch (e: Exception) {
                Log.e(TAG_NET, "停止音频播放失败，但继续执行后续步骤", e)
                // 继续执行，不让异常中断流程
            }
            
            // 2. 取消SSE连接
            try {
                Log.i(TAG_NET, "取消SSE连接")
                val eventSource = currentEventSource
                if (eventSource != null) {
                    try {
                        eventSource.cancel()
                        Log.i(TAG_NET, "SSE连接取消成功")
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "取消SSE连接时出错", e)
                    } finally {
                        currentEventSource = null
                    }
                } else {
                    Log.w(TAG_NET, "当前SSE连接为null，无需取消")
                }
            } catch (e: Exception) {
                Log.e(TAG_NET, "取消SSE连接失败，但继续执行后续步骤", e)
                // 继续执行，不让异常中断流程
            }
            
            // 3. 重置状态 - 这一步很少出错，但为安全起见也加上try-catch
            try {
                isPlaying = false
                isProcessingRequest = false
                Log.i(TAG_NET, "状态重置成功")
            } catch (e: Exception) {
                Log.e(TAG_NET, "重置状态失败，但继续执行后续步骤", e)
            }
            
            // 隐藏问答文本
            try {
                if (!isFinishing && !isDestroyed) {
                    // 修改: 不再延迟隐藏文本，直接隐藏
                    hideQuestionAnswerText()
                    Log.i(TAG_NET, "问答文本隐藏成功")
                }
            } catch (e: Exception) {
                Log.e(TAG_NET, "隐藏问答文本失败，但继续执行后续步骤", e)
            }
            
            // 立即显示建议问题
            try {
                Log.i(TAG_NET, "立即显示建议问题")
                showSuggestedQuestions()
            } catch (e: Exception) {
                Log.e(TAG_NET, "显示建议问题失败", e)
            }
            
            // 4. 清理缓存的音频片段
            try {
                Log.i(TAG_NET, "清理缓存的音频片段")
                try {
                    audioChunks.clear()
                } catch (e: Exception) {
                    Log.e(TAG_NET, "清理音频片段失败", e)
                }
                
                try {
                    audioTextMap.clear() // 清理音频-文本映射
                } catch (e: Exception) {
                    Log.e(TAG_NET, "清理音频文本映射失败", e)
                }
                
                try {
                    questionMap.clear() // 清理问题映射
                } catch (e: Exception) {
                    Log.e(TAG_NET, "清理问题映射失败", e)
                }
                
                currentChunkIndex = 0
                Log.i(TAG_NET, "音频缓存清理成功")
            } catch (e: Exception) {
                Log.e(TAG_NET, "清理音频缓存失败，但继续执行后续步骤", e)
            }
            
            // 5. 立即开始录音 - 这一步可能会出现异常
            var recordingStarted = false
            try {
                Log.i(TAG_NET, "尝试启动录音")
                // 检查Activity是否已经销毁
                if (isFinishing || isDestroyed) {
                    Log.w(TAG_NET, "Activity已销毁，不再启动录音")
                    return
                }
                
                // 先设置UI状态
                try {
                    if (binding != null) {
                        binding?.btnRecord?.isEnabled = true
                        binding?.recordingStatus?.text = "录音中..." // 确保显示"录音中..."
                    } else {
                        Log.e(TAG_NET, "binding为null，无法更新UI")
                    }
                } catch (e: Exception) {
                    Log.e(TAG_NET, "更新UI状态失败", e)
                }
                
                // 检查Minerva实例
                if (minerva == null) {
                    Log.w(TAG_NET, "Minerva录音实例为null，尝试重新初始化")
                    try {
                        initMinerva() // 尝试重新初始化
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "初始化Minerva失败", e)
                    }
                }
                
                // 设置录音状态
                isRecording = true
                
                // 启动录音 - 确保只在当前Activity未销毁时启动录音
                if (!isFinishing && !isDestroyed && minerva != null) {
                    try {
                        minerva?.start()
                        recordingStarted = true
                        Log.i(TAG_NET, "录音成功启动")
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "启动录音失败", e)
                        recordingStarted = false
                    }
                } else {
                    Log.w(TAG_NET, "无法启动录音: Activity已销毁或Minerva为null")
                }
            } catch (e: Exception) {
                Log.e(TAG_NET, "启动录音失败，但继续执行后续步骤", e)
                // 如果录音启动失败，尝试恢复到等待状态
                isRecording = false
                try {
                    if (!isFinishing && !isDestroyed && binding != null) {
                        binding?.btnRecord?.isEnabled = false
                        updateUIState("等待语音...", false, "录音启动失败，恢复到等待状态")
                    }
                } catch (e2: Exception) {
                    Log.e(TAG_NET, "更新UI状态失败", e2)
                }
            }
            
            // 6. 重新启动语音检测 - 这一步也可能会出现异常
            try {
                if (!isFinishing && !isDestroyed) {
                    Log.i(TAG_NET, "尝试重启语音检测")
                    // 先安全停止现有检测
                    try {
                        if (voiceDetector != null) {
                            voiceDetector?.stopListening()
                        }
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "停止语音检测失败", e)
                    }
                    
                    // 如果检测器为null，尝试重新初始化
                    if (voiceDetector == null) {
                        Log.w(TAG_NET, "语音检测器为null，尝试重新初始化")
                        try {
                            initVoiceDetection()
                        } catch (e: Exception) {
                            Log.e(TAG_NET, "初始化语音检测失败", e)
                        }
                    }
                    
                    // 启动语音检测
                    if (voiceDetector != null) {
                        try {
                            voiceDetector?.startListening(object : AudioStreamObserver {
                                override fun onVoiceStart() {
                                    Log.i(TAG_NET, "检测到语音开始")
                                }

                                override fun onVoiceEnd(silenceDuration: Long) {
                                    Log.i(TAG_NET, "检测到语音结束，持续时间：$silenceDuration ms")
                                    if (isRecording && !isPlaying && !isProcessingRequest) {
                                        isRecording = false
                                        try {
                                            minerva?.stop()
                                        } catch (e: Exception) {
                                            Log.e(TAG_NET, "停止录音失败", e)
                                        }
                                    }
                                }

                                override fun onError(e: Exception) {
                                    Log.e(TAG_NET, "语音检测错误", e)
                                }
                            })
                            Log.i(TAG_NET, "语音检测重启成功")
                        } catch (e: Exception) {
                            Log.e(TAG_NET, "启动语音检测失败", e)
                        }
                    } else {
                        Log.w(TAG_NET, "语音检测器为null，无法启动")
                    }
                } else {
                    Log.w(TAG_NET, "Activity已销毁，不再重启语音检测")
                }
            } catch (e: Exception) {
                Log.e(TAG_NET, "重启语音检测失败", e)
            }
            
            // 显示Toast提示
            try {
                if (!isFinishing && !isDestroyed) {
                    try {
                        // 移除Toast，保留日志记录
                        Log.i(TAG_NET, "显示打断提示")
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "显示打断提示失败", e)
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG_NET, "显示Toast失败", e)
            }
            
            // 添加：延迟隐藏建议问题
            try {
                // 延迟30秒后，如果仍未检测到有效语音，才隐藏建议问题
                if (!isFinishing && !isDestroyed) {
                    val handler = Handler(Looper.getMainLooper())
                    handler.postDelayed({
                        try {
                            // 仅当当前没有播放且没有处理请求时才隐藏
                            if (!isPlaying && !isProcessingRequest && !isFinishing && !isDestroyed) {
                                Log.i(TAG_NET, "30秒内未检测到语音，隐藏建议问题")
                                hideSuggestedQuestions()
                            }
                        } catch (e: Exception) {
                            Log.e(TAG_NET, "延迟隐藏建议问题失败", e)
                        }
                    }, 30000) // 30秒延迟
                }
            } catch (e: Exception) {
                Log.e(TAG_NET, "设置延迟隐藏建议问题失败", e)
            }
            
            // 防止连续打断
            try {
                if (!isFinishing && !isDestroyed && binding != null) {
                    binding?.btnRecord?.isEnabled = false
                    val handler = Handler(Looper.getMainLooper())
                    handler.postDelayed({
                        try {
                            if (!isFinishing && !isDestroyed) {
                                binding?.btnRecord?.isEnabled = true
                                if (isRecording) {
                                    binding?.recordingStatus?.text = "录音中..."
                                } else if (!isProcessingRequest) {
                                    // 确保此时如果不在录音状态且不在处理请求，再次开始录音
                                    isRecording = true
                                    binding?.recordingStatus?.text = "录音中..."
                                    try {
                                        minerva?.start()
                                    } catch (e: Exception) {
                                        Log.e(TAG_NET, "延迟启动录音失败", e)
                                    }
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG_NET, "延迟更新UI状态失败", e)
                        }
                    }, 1000) // 延迟1秒后重新启用按钮
                }
            } catch (e: Exception) {
                Log.e(TAG_NET, "设置按钮延迟启用失败", e)
            }
            
            // 打印最终状态
            Log.i(TAG_NET, "打断操作完成，当前状态: isRecording=$isRecording, isPlaying=$isPlaying, isProcessingRequest=$isProcessingRequest, 录音是否启动=$recordingStarted")
            
        } catch (e: Exception) {
            // 捕获所有可能的异常，确保不会因为打断操作导致Activity崩溃
            Log.e(TAG_NET, "打断播放过程中发生未预期的异常", e)
            try {
                // 尝试恢复到录音状态
                isPlaying = false
                isProcessingRequest = false
                isInterrupted = true
                
                // 确保Activity未销毁时才执行UI更新和录音启动
                if (!isFinishing && !isDestroyed && binding != null) {
                    binding?.btnRecord?.isEnabled = true
                    binding?.recordingStatus?.text = "录音中..."
                    
                    // 检查Minerva实例
                    if (minerva == null) {
                        Log.w(TAG_NET, "异常恢复: Minerva录音实例为null，尝试重新初始化")
                        try {
                            initMinerva() // 尝试重新初始化
                        } catch (e2: Exception) {
                            Log.e(TAG_NET, "异常恢复: 初始化Minerva失败", e2)
                        }
                    }
                    
                    // 尝试启动录音
                    isRecording = true
                    if (minerva != null) {
                        try {
                            minerva?.start()
                        } catch (e2: Exception) {
                            Log.e(TAG_NET, "异常恢复: 启动录音失败", e2)
                        }
                    }
                } else {
                    Log.w(TAG_NET, "Activity已销毁，不再尝试恢复状态")
                }
            } catch (e2: Exception) {
                Log.e(TAG_NET, "恢复状态失败", e2)
                // 最后的安全网：如果恢复失败，至少不要崩溃
            }
        }
    }
    
    // 安全地调用DUIX的stopAudio方法
    private fun safeStopAudio() {
        try {
            if (duix != null) {
                duix?.stopAudio()
                Log.i(TAG_NET, "成功停止音频播放")
            } else {
                Log.w(TAG_NET, "DUIX实例为null，无法停止音频")
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "停止音频时发生异常", e)
            // 异常已捕获，不再往上抛出
        }
    }
    
    // 处理返回按键按下的情况
    @Override
    override fun onBackPressed() {
        try {
            // 如果正在播放，先停止播放
            if (isPlaying) {
                try {
                    Log.i(TAG_NET, "返回按钮被按下，停止当前播放")
                    safeStopAudio()
                    isPlaying = false
                    isInterrupted = true
                } catch (e: Exception) {
                    Log.e(TAG_NET, "停止播放失败", e)
                }
            }
            
            // 确保取消所有网络请求
            try {
                currentEventSource?.cancel()
                currentEventSource = null
            } catch (e: Exception) {
                Log.e(TAG_NET, "取消SSE连接失败", e)
            }
            
            // 调用父类方法完成返回操作
            super.onBackPressed()
        } catch (e: Exception) {
            Log.e(TAG_NET, "处理返回按钮时发生异常", e)
            // 即使发生异常，也要尝试调用父类方法
            try {
                super.onBackPressed()
            } catch (e2: Exception) {
                Log.e(TAG_NET, "调用父类onBackPressed失败", e2)
                // 最后手段，直接结束Activity
                finish()
            }
        }
    }

    private fun initOk() {
        Log.i(TAG_NET, "数字人初始化完成") 
        if (checkRecordPermission()) {
            // 只有当minerva为null时才初始化
            if (minerva == null) {
                initMinerva()
            }
            
            runOnUiThread {
                binding?.btnRecord?.isEnabled = false
                binding?.recordingStatus?.text = "等待语音..."
            }
            
            // 只有当voiceDetector为null时才初始化
            if (voiceDetector == null) {
                initVoiceDetection()
            }
            
            // 请求并显示初始建议问题
            fetchInitialSuggestions()
        }
    }
    
    // 请求初始建议问题
    private fun fetchInitialSuggestions() {
        Log.i(TAG_NET, "请求初始建议问题")
        
        val deviceId = DeviceIdUtil.getDeviceId(this)
        val requestBuilder = Request.Builder()
            .url("${ttsUrl.replace("/tts", "")}/parameters")
            .get()
        
        // 添加请求头
        requestBuilder.addHeader("DIFY_API_KEY", apiKey)
        requestBuilder.addHeader("REFERENCE_ID", reference_id ?: "")
        requestBuilder.addHeader("USER_ID", deviceId)
        requestBuilder.addHeader("Accept", "text/event-stream") // 添加SSE支持的Header
        
        val request = requestBuilder.build()
        
        // 创建支持SSE的client
        val sseClient = okHttpClient.newBuilder()
            .readTimeout(0, TimeUnit.MILLISECONDS) // SSE需要无限超时
            .build()
            
        val listener = object : EventSourceListener() {
            override fun onOpen(eventSource: EventSource, response: Response) {
                Log.i(TAG_NET, "初始化SSE连接已打开: ${response.code}")
            }
            
            override fun onEvent(eventSource: EventSource, id: String?, type: String?, data: String) {
                Log.i(TAG_NET, "收到初始化SSE事件: $data")
                try {
                    val jsonNode = objectMapper.readTree(data)
                    val event = jsonNode.get("event")?.asText()
                    
                    // 处理初始化事件，对话未开始时使用suggested_questions
                    if (event == "parameters" && !hasConversationStarted) {
                        val suggestionsNode = jsonNode.get("suggested_questions")
                        if (suggestionsNode != null && suggestionsNode.isArray) {
                            currentSuggestedQuestions.clear()
                            for (i in 0 until suggestionsNode.size()) {
                                val suggestion = suggestionsNode[i].asText()
                                if (suggestion != null && suggestion.isNotEmpty()) {
                                    currentSuggestedQuestions.add(suggestion)
                                }
                            }
                            Log.i(TAG_NET, "初始化: 收到parameters中的建议问题: $currentSuggestedQuestions")
                            
                            // 显示建议问题
                            showSuggestedQuestions()
                        }
                        
                        // 处理欢迎语
                        val openingStatement = jsonNode.get("opening_statement")?.asText()
                        if (openingStatement != null && openingStatement.isNotEmpty()) {
                            Log.i(TAG_NET, "收到欢迎语: $openingStatement")
                            // 显示欢迎语
                            showWelcomeMessage(openingStatement)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG_NET, "解析初始化SSE事件失败", e)
                }
            }
            
            override fun onClosed(eventSource: EventSource) {
                Log.i(TAG_NET, "初始化SSE连接已关闭")
            }
            
            override fun onFailure(eventSource: EventSource, t: Throwable?, response: Response?) {
                Log.e(TAG_NET, "初始化SSE连接失败: ${t?.message}", t)
            }
        }
        
        // 启动SSE连接
        EventSources.createFactory(sseClient).newEventSource(request, listener)
    }
    
    // 显示欢迎消息
    private fun showWelcomeMessage(message: String) {
        Log.i(TAG_NET, "显示欢迎消息: $message")
        
        // 如果已经在播放或处理中，不显示欢迎语
        if (isPlaying || isProcessingRequest) {
            Log.i(TAG_NET, "当前正在播放或处理中，不显示欢迎消息")
            return
        }
        
        // 显示欢迎消息为回答
        runOnUiThread {
            binding?.cvDialogContainer?.visibility = View.VISIBLE
            binding?.llQuestionContainer?.visibility = View.GONE
            binding?.tvAnswer?.text = message
        }
        
        // 几秒后自动隐藏
        textHideHandler.removeCallbacksAndMessages(null)
        textHideHandler.postDelayed({
            hideQuestionAnswerText()
        }, TEXT_DISPLAY_DURATION)
    }

    /**
     * 播放16k采样率单通道16位深的wav本地文件
     */
    private fun playWav() {
        val wavName = "voice4.wav"
        val wavDir = File(mContext.getExternalFilesDir("duix"), "wav")
        if (!wavDir.exists()) {
            wavDir.mkdirs()
        }
        val wavFile = File(wavDir, wavName)
        if (!wavFile.exists()) { // 拷贝到sdcard
            val executor = Executors.newSingleThreadExecutor()
            executor.execute {
                val input = mContext.assets.open("wav/${wavName}")
                val out: OutputStream = FileOutputStream("${wavFile.absolutePath}.tmp")
                val buffer = ByteArray(1024)
                var read: Int
                while (input.read(buffer).also { read = it } != -1) {
                    out.write(buffer, 0, read)
                }
                input.close()
                out.close()
                File("${wavFile.absolutePath}.tmp").renameTo(wavFile)
                duix?.playAudio(wavFile.absolutePath)
            }
        } else {
            duix?.playAudio(wavFile.absolutePath)
        }
    }

    private fun playWav(wavName: String) {
        val wavDir = File(mContext.getExternalFilesDir("duix"), "wav")
        if (!wavDir.exists()) {
            wavDir.mkdirs()
        }
        val wavFile = File(wavDir, wavName)
        if (!wavFile.exists()) { // 拷贝到sdcard
            val executor = Executors.newSingleThreadExecutor()
            executor.execute {
                val input = mContext.assets.open("wav/${wavName}")
                val out: OutputStream = FileOutputStream("${wavFile.absolutePath}.tmp")
                val buffer = ByteArray(1024)
                var read: Int
                while (input.read(buffer).also { read = it } != -1) {
                    out.write(buffer, 0, read)
                }
                input.close()
                out.close()
                File("${wavFile.absolutePath}.tmp").renameTo(wavFile)
                duix?.playAudio(wavFile.absolutePath)
            }
        } else {
            duix?.playAudio(wavFile.absolutePath)
        }
    }

    private fun initMinerva() {
        if (!checkRecordPermission()) {
            return
        }
        
        // 如果已经初始化，则不再初始化
        if (minerva != null) {
            Log.i(TAG_NET, "录音控制器已存在，不再重复初始化")
            return
        }
        
        try {
            // 创建音频存储目录
            val recordDir = File(mFilePath)
            if (!recordDir.exists()) {
                recordDir.mkdirs()
            }
            
            Log.i(TAG_NET, "正在初始化录音功能...")
            
            // 初始化Minerva
            minerva = MinervaAgent.recording()
                .setSampleRate(16000)
                .setChannel(AudioFormat.CHANNEL_IN_MONO)
                .setEncoding(AudioFormat.ENCODING_PCM_16BIT)
                .setSaveDirPath(mFilePath)
                .setAudioFormat(AudioFormats.WAV)
                .setOnRecordingStatesListener(object : OnRecordingStatesListener {
                    override fun onStateChange(state: com.lodz.android.minerva.bean.states.RecordingStates) {
                        when (state) {
                            is Recording -> {
                                // 正在录音
                                Log.i(TAG_NET, "Minerva开始录音")
                                // 如果已经在播放状态，不要更新UI
                                if (!isPlaying) {
                                    runOnUiThread {
                                        binding?.btnRecord?.isEnabled = true
                                        binding?.recordingStatus?.text = "录音中..."
                                    }
                                }
                            }
                            is Finish -> {
                                // 录音完成，检查音频有效性
                                Log.i(TAG_NET, "录音完成: ${state.file?.absolutePath}")
                                // 重置录音状态
                                isRecording = false
                                
                                // 如果已经在播放状态，不处理录音文件
                                if (isPlaying) {
                                    Log.i(TAG_NET, "当前在播放状态，忽略录音结果")
                                    runOnUiThread {
                                        binding?.btnRecord?.isEnabled = true
                                        updateUIState("点击打断", true, "当前在播放状态，保持打断按钮状态")
                                    }
                                    return@onStateChange
                                }
                                
                                if (state.file == null || !state.file.exists()) {
                                    Log.e(TAG_NET, "录音文件不存在")
                                    // 移除Toast
                                    resetRecordingState()
                                    return@onStateChange
                                }

                                // 检查文件大小
                                if (state.file.length() < 1000) {
                                    Log.e(TAG_NET, "录音文件太小，可能是静音")
                                    // 移除Toast
                                    resetRecordingState()
                                    return@onStateChange
                                }

                                // 检查音频是否包含有效声音
                                if (!isValidAudio(state.file)) {
                                    Log.e(TAG_NET, "检测到静音或无效音频")
                                    // 移除Toast
                                    resetRecordingState()
                                    state.file.delete() // 删除无效音频文件
                                    return@onStateChange
                                }
                                
                                // 只有检测到有效语音时，才发送到服务器
                                Log.i(TAG_NET, "检测到有效语音，准备发送到服务器")
                                // Toast.makeText(mContext, "检测到有效语音", Toast.LENGTH_SHORT).show()
                                
                                // 设置对话已开始标志
                                hasConversationStarted = true
                                
                                sendAudioToServer(state.file)
                            }
                            else -> {
                                // 其他状态
                                Log.d(TAG_NET, "其他录音状态: $state")
                            }
                        }
                    }
                })
                .build(this)
            
            Log.i(TAG_NET, "录音功能初始化完成")
            
            // 设置UI状态
            runOnUiThread {
                binding?.btnRecord?.isEnabled = false
                updateUIState("等待语音...", false, "录音功能初始化完成，等待语音输入")
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "录音功能初始化失败", e)
            // 失败后尝试延迟重试
            binding?.btnRecord?.postDelayed({
                if (!isFinishing) {
                    initMinerva()
                }
            }, 2000)
        }
    }

    // 添加重置录音状态的方法
    private fun resetRecordingState() {
        Log.i(TAG_NET, "重置录音状态: isRecording=$isRecording, isPlaying=$isPlaying, isProcessingRequest=$isProcessingRequest")
        
        // 只有在非播放状态下才重置UI
        if (!isPlaying) {
            if (!isProcessingRequest) {
                // 如果不在处理请求，直接启动录音
                isRecording = true
                updateUIState("录音中...", true, "重置录音状态为录音中") // 改为录音中
                
                // 启动录音
                minerva?.start()
            } else {
                // 在处理请求时显示处理中
                isRecording = false
                updateUIState("处理中...", false, "处理请求中")
            }
        } else {
            Log.i(TAG_NET, "当前在播放状态，保持UI为打断状态")
            updateUIState("点击打断", true, "保持打断状态")
        }
    }

    // 增强版音频有效性检测
    private fun isValidAudio(audioFile: File): Boolean {
        try {
            // 首先检查文件大小
            val fileSize = audioFile.length()
            if (fileSize < 2000) { // 进一步减少最小文件大小要求到2KB
                Log.e(TAG_NET, "文件太小，可能无效: $fileSize bytes (最低要求2000字节)")
                return false
            }
            
            // 音频振幅检测
            val audioData = audioFile.readBytes()
            var maxAmplitude = 0.0
            var sumAmplitude = 0.0
            var framesAboveThreshold = 0
            var totalFrames = 0
            
            val sampleRate = 16000 // 采样率16kHz
            val frameSize = 320 // 20ms窗口
            val threshold = amplitudeThreshold.toDouble() // 使用用户设置的振幅阈值
            
            // 跳过WAV文件头（44字节）
            var offset = 44
            
            // 确保文件至少有60毫秒的音频（至少3帧）
            val minFrames = 3
            
            // 收集所有帧的振幅数据，用于统计分析
            val frameAmplitudes = mutableListOf<Double>()
            
            var i = offset
            while (i < audioData.size - 1) {
                var frameSum = 0.0
                var frameSamples = 0
                
                // 处理一帧数据
                val end = minOf(i + frameSize * 2, audioData.size - 1)
                var j = i
                while (j < end) {
                    if (j + 1 < audioData.size) {
                        val sample = (audioData[j + 1].toInt() shl 8) or (audioData[j].toInt() and 0xFF)
                        val amplitude = Math.abs(sample).toDouble()
                        frameSum += amplitude
                        frameSamples++
                        maxAmplitude = Math.max(maxAmplitude, amplitude)
                    }
                    j += 2
                }
                
                if (frameSamples > 0) {
                    val frameAvg = frameSum / frameSamples
                    frameAmplitudes.add(frameAvg)
                    sumAmplitude += frameAvg
                    totalFrames++
                    
                    if (frameAvg > threshold) {
                        framesAboveThreshold++
                    }
                }
                
                i += frameSize * 2
            }
            
            // 如果总帧数太少，也认为是无效音频
            if (totalFrames < minFrames) {
                Log.e(TAG_NET, "音频时长太短: $totalFrames 帧 (最低要求 $minFrames 帧)")
                return false
            }
            
            // 排序振幅进行更复杂的分析
            val sortedAmplitudes = frameAmplitudes.sorted()
            
            // 计算环境噪音水平（取最低75%的平均值）
            val noiseLevel = if (sortedAmplitudes.size > 4) {
                val noiseFrames = sortedAmplitudes.subList(0, (sortedAmplitudes.size * 0.75).toInt())
                noiseFrames.average()
            } else {
                0.0
            }
            
            // 计算信噪比：峰值与噪音水平的比值
            val signalToNoiseRatio = if (noiseLevel > 0) maxAmplitude / noiseLevel else maxAmplitude
            
            val avgAmplitude = if (totalFrames > 0) sumAmplitude / totalFrames else 0.0
            val framesRatio = if (totalFrames > 0) framesAboveThreshold.toDouble() / totalFrames else 0.0
            
            Log.i(TAG_NET, "音频分析结果: 最大振幅=$maxAmplitude, 平均振幅=$avgAmplitude, " +
                    "有效帧比例=${framesRatio * 100}%, 总帧数=$totalFrames, 有效帧数=$framesAboveThreshold, " +
                    "环境噪音水平=$noiseLevel, 信噪比=$signalToNoiseRatio")
            
            // 判断标准：
            // 1. 最大振幅超过阈值
            // 2. 有一定比例的帧超过阈值
            // 3. 信噪比足够高（至少2.5，表示最大振幅是噪音的2.5倍）
            val minValidRatio = validFrameRatio / 100.0 // 转换为小数比例
            val isValid = maxAmplitude > threshold && 
                          framesRatio > minValidRatio && 
                          signalToNoiseRatio > 2.5
            
            if (!isValid) {
                Log.e(TAG_NET, "音频无效: 最大振幅=$maxAmplitude (阈值=$threshold), " +
                        "有效帧比例=${framesRatio * 100}% (要求>${validFrameRatio}%), " +
                        "信噪比=$signalToNoiseRatio (要求>2.5)")
            } else {
                Log.i(TAG_NET, "音频有效，将发送到服务器处理")
            }
            
            return isValid
        } catch (e: Exception) {
            Log.e(TAG_NET, "音频有效性检测失败", e)
            return false
        }
    }

    private fun updateRecordButton(text: String) {
        runOnUiThread {
            binding?.recordingStatus?.text = text
        }
    }

    private fun playWaitVoice() {
        if (voiceType == MainActivity.woman_key) {
            playWav("wait_woman.mp3")
        } else {
            playWav("wait_man.mp3")
        }
    }

    // 检查是否可以发送请求（防抖控制）
    private fun canSendRequest(): Boolean {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastRequestTime < requestCooldownTime) {
            Log.i(TAG_NET, "请求过于频繁，跳过本次请求。距离上次请求：${currentTime - lastRequestTime}ms")
            return false
        }
        return true
    }

    private fun sendAudioToServer(audioFile: File) {
        // 检查请求频率
        if (!canSendRequest()) {
            Log.i(TAG_NET, "请求被防抖机制阻止，继续录音状态")
            // 继续保持录音状态，不发送请求
            runOnUiThread {
                updateUIState("录音中...", true, "请求被防抖阻止，继续录音")
            }
            return
        }

        lastRequestTime = System.currentTimeMillis()
        isProcessingRequest = true
        isInterrupted = false
        currentChunkIndex = 0
        audioChunks.clear()
        
        // 添加：每次发送新的音频请求时清除之前的问题文本
        // 这样可以确保每次新的语音输入都重新开始收集问题文本
        questionText = null
        Log.i(TAG_NET, "清除之前的问题文本，准备接收新问题")
        
        runOnUiThread {
            binding?.btnRecord?.isEnabled = false
            binding?.recordingStatus?.text = "请让我思考一下..."
        }

        // 打印请求信息
        Log.i(TAG_NET, "开始发送请求")
        LogUtils.getInstance(this).log("请求URL: $ttsUrl")
        Log.i(TAG_NET, "请求URL: $ttsUrl")
        Log.i(TAG_NET, "音频文件: ${audioFile.absolutePath}")
        Log.i(TAG_NET, "文件大小: ${audioFile.length()} bytes")
        Log.i(TAG_NET, "使用音色: $reference_id")
        val deviceId = DeviceIdUtil.getDeviceId(this)
        
        // 检查DUIX SDK状态
        if (duix == null) {
            Log.e(TAG_NET, "DUIX SDK未初始化")
            // 移除Toast
            return
        }
        
        val requestBody = MultipartBody.Builder().setType(MultipartBody.FORM)
            .addFormDataPart("audio_file", audioFile.name, audioFile.asRequestBody("audio/wav".toMediaTypeOrNull()))
            .addFormDataPart("DIFY_API_KEY", apiKey)
            .addFormDataPart("REFERENCE_ID", reference_id ?: "")
            .addFormDataPart("USER_ID", deviceId)
            .build()

        val requestBuilder = Request.Builder().url(ttsUrl).post(requestBody)

        // 添加请求头
        requestBuilder.addHeader("DIFY_API_KEY", apiKey)
        requestBuilder.addHeader("REFERENCE_ID", reference_id ?: "")
        requestBuilder.addHeader("USER_ID", deviceId)
        requestBuilder.addHeader("Accept", "text/event-stream") // 添加SSE支持的Header

        val request = requestBuilder.build()

        // 创建支持SSE的client
        val sseClient = okHttpClient.newBuilder()
            .readTimeout(0, TimeUnit.MILLISECONDS) // SSE需要无限超时
            .build()

        val listener = object : EventSourceListener() {
            override fun onOpen(eventSource: EventSource, response: Response) {
                Log.i(TAG_NET, "SSE连接已打开: ${response.code}")
                currentEventSource = eventSource
                runOnUiThread {
                    binding?.recordingStatus?.text = "请让我思考一下..."
                    binding?.btnRecord?.isEnabled = false
                }
            }

            override fun onEvent(eventSource: EventSource, id: String?, type: String?, data: String) {
                // 复用与sendTextToServer相同的事件处理逻辑
                Log.i(TAG_NET, "收到SSE事件: $data")
                if (isInterrupted) {
                    Log.i(TAG_NET, "已被打断，忽略SSE事件")
                    return
                }

                try {
                    val jsonNode = objectMapper.readTree(data)
                    val event = jsonNode.get("event")?.asText()
                    val status = jsonNode.get("status")?.asText()
                    
                    Log.i(TAG_NET, "解析事件: event=$event, status=$status")
                    
                    // 修改建议问题的处理逻辑
                    // 如果对话已经开始(一定是在这个方法中)，且存在data字段，优先使用data字段
                    if (jsonNode.has("data") && jsonNode.get("data")?.isArray == true) {
                        // 优先处理data格式的建议问题
                        val dataNode = jsonNode.get("data")
                        currentSuggestedQuestions.clear()
                        for (i in 0 until dataNode.size()) {
                            val suggestion = dataNode[i].asText()
                            if (suggestion != null && suggestion.isNotEmpty()) {
                                currentSuggestedQuestions.add(suggestion)
                            }
                        }
                        Log.i(TAG_NET, "收到data格式的建议问题(会话中): $currentSuggestedQuestions")
                        
                        // 如果当前已经播放完毕，立即显示建议问题
                        if (!isPlaying && !isProcessingRequest) {
                            showSuggestedQuestions()
                        }
                    }
                    // 如果没有data字段但有根级别的suggested_questions，且对话未开始，才使用suggested_questions
                    else if (jsonNode.has("suggested_questions") && jsonNode.get("suggested_questions")?.isArray == true && !hasConversationStarted) {
                        // 处理根级别的suggested_questions
                        val suggestionsNode = jsonNode.get("suggested_questions")
                        if (suggestionsNode != null) {
                            currentSuggestedQuestions.clear()
                            for (i in 0 until suggestionsNode.size()) {
                                val suggestion = suggestionsNode[i].asText()
                                if (suggestion != null && suggestion.isNotEmpty()) {
                                    currentSuggestedQuestions.add(suggestion)
                                }
                            }
                            Log.i(TAG_NET, "收到根级别的建议问题(初始化): $currentSuggestedQuestions")
                            
                            // 如果当前已经播放完毕，立即显示建议问题
                            if (!isPlaying && !isProcessingRequest) {
                                showSuggestedQuestions()
                            }
                        }
                    }
                    // 如果上述两个字段都没有，但是event是parameters，则检查参数中的suggested_questions
                    else if (event == "parameters") {
                        // 只有在对话未开始时才处理parameters事件中的suggested_questions
                        if (!hasConversationStarted) {
                            val suggestionsNode = jsonNode.get("suggested_questions")
                            if (suggestionsNode != null && suggestionsNode.isArray) {
                                currentSuggestedQuestions.clear()
                                for (i in 0 until suggestionsNode.size()) {
                                    val suggestion = suggestionsNode[i].asText()
                                    if (suggestion != null && suggestion.isNotEmpty()) {
                                        currentSuggestedQuestions.add(suggestion)
                                    }
                                }
                                Log.i(TAG_NET, "收到parameters中的建议问题(初始化): $currentSuggestedQuestions")
                                
                                // 显示建议问题
                                showSuggestedQuestions()
                            }
                            
                            // 处理欢迎语
                            val openingStatement = jsonNode.get("opening_statement")?.asText()
                            if (openingStatement != null && openingStatement.isNotEmpty()) {
                                Log.i(TAG_NET, "收到欢迎语: $openingStatement")
                                // 显示欢迎语
                                showWelcomeMessage(openingStatement)
                            }
                        } else {
                            Log.i(TAG_NET, "对话已开始，忽略parameters中的建议问题")
                        }
                    } else if (event == "message") {
                        val url = jsonNode.get("url")?.asText()
                        val answer = jsonNode.get("answer")?.asText()
                        val messageStatus = jsonNode.get("status")?.asText()
                        val question = jsonNode.get("question")?.asText()
                        
                        // 在处理message事件时，设置对话已开始标志
                        if ((question != null && question.isNotEmpty()) || 
                            (answer != null && answer.isNotEmpty())) {
                            hasConversationStarted = true
                        }
                        
                        // 添加：只有在满足特定条件时更新问题文本
                        // 当收到answer和question一起来时，且answer不为空，说明这是一个有效的问答对
                        if (question != null && question.isNotEmpty() && 
                            answer != null && answer.isNotEmpty()) {
                            hasValidResponse = true // 收到有效回答
                            questionText = question
                            Log.i(TAG_NET, "收到有效问答对，更新问题文本: $question")
                        }
                        
                        // 尝试解析消息中的建议问题，即便对话已开始，message中的建议问题也可能有效
                        val suggestionsNode = jsonNode.get("suggested_questions")
                        if (suggestionsNode != null && suggestionsNode.isArray) {
                            currentSuggestedQuestions.clear()
                            for (i in 0 until suggestionsNode.size()) {
                                val suggestion = suggestionsNode[i].asText()
                                if (suggestion != null && suggestion.isNotEmpty()) {
                                    currentSuggestedQuestions.add(suggestion)
                                }
                            }
                            Log.i(TAG_NET, "收到message中的建议问题: $currentSuggestedQuestions")
                        }
                        
                        Log.i(TAG_NET, "解析消息内容: url=$url, answer=$answer, status=$messageStatus, question=$question")
                        
                        // 处理不同状态的消息
                        when (messageStatus) {
                            "ready" -> {
                                // ready状态通常是服务器准备好了，但还没有生成音频
                                if (question != null && question.isNotEmpty()) {
                                    Log.i(TAG_NET, "服务器已准备好处理请求: $question")
                                    // 修改：只在第一次保存问题文本，或者与有效回答配对时才更新
                                    if (questionText == null || (answer != null && answer.isNotEmpty())) {
                                        questionText = question
                                        Log.i(TAG_NET, "保存问题文本到全局变量: $question")
                                    } else {
                                        Log.i(TAG_NET, "忽略无关的问题文本更新: $question, 保留原问题: $questionText")
                                    }
                                } else {
                                    Log.i(TAG_NET, "服务器已准备好处理请求")
                                }
                                // 这种状态下URL可能为空，这是正常的
                                if (url == null || url.isEmpty()) {
                                    Log.i(TAG_NET, "ready状态下URL为空是正常的，等待后续音频URL")
                                }
                            }
                            "ok" -> {
                                if (url != null && url.isNotEmpty()) {
                                    // 正常处理带有URL的消息
                                    if (question != null && question.isNotEmpty()) {
                                        Log.i(TAG_NET, "收到对问题的回答: $answer")
                                        // 添加：只有当回答不为空时，才更新问题文本
                                        if (answer != null && answer.isNotEmpty()) {
                                            questionText = question
                                            Log.i(TAG_NET, "更新问题文本(带有回答): $question")
                                        }
                                    } else {
                                        Log.i(TAG_NET, "收到随机回答: $answer")
                                    }
                                    
                                    // 直接在主线程中下载音频
                                    try {
                                        Log.i(TAG_NET, "开始下载音频: $url")
                                        val audioChunk = downloadAudioChunk(url)
                                        if (audioChunk != null) {
                                            // 将音频文件与问题和回答文本关联
                                            if (answer != null && answer.isNotEmpty()) {
                                                audioTextMap[audioChunk] = answer
                                                Log.i(TAG_NET, "存储回答文本: ${answer.take(20)}...")
                                            }
                                            if (question != null && question.isNotEmpty() && answer != null && answer.isNotEmpty()) {
                                                // 修改：只有当有回答时，才使用当前消息的问题存储
                                                questionMap[audioChunk] = question
                                                Log.i(TAG_NET, "存储问题文本(当前消息): ${question.take(20)}...")
                                            } else if (questionText != null && questionText!!.isNotEmpty()) {
                                                // 如果当前消息没有问题或没有回答，但之前有保存的问题，使用之前的问题
                                                questionMap[audioChunk] = questionText!!
                                                Log.i(TAG_NET, "使用之前保存的问题文本: ${questionText?.take(20)}...")
                                            }
                                            
                                            audioChunks.add(audioChunk)
                                            Log.i(TAG_NET, "音频片段已添加到队列，当前队列大小: ${audioChunks.size}")
                                            
                                            
                                            // 如果是第一个片段，立即播放
                                            if (audioChunks.size == 1 && !isInterrupted) {
                                                Log.i(TAG_NET, "准备播放第一个音频片段")
                                                playAudioChunk(audioChunk)
                                            }
                                        } else {
                                            Log.e(TAG_NET, "音频片段下载失败，URL: $url")
                                        }
                                    } catch (e: Exception) {
                                        Log.e(TAG_NET, "下载音频片段失败: ${e.message}", e)
                                        e.printStackTrace()
                                    }
                                } else {
                                    Log.w(TAG_NET, "状态为ok但URL为空")
                                    // 尝试从其他字段获取有用信息
                                    if (answer != null && answer.isNotEmpty()) {
                                        Log.i(TAG_NET, "收到回答但无音频: $answer")
                                    }
                                }
                            }
                            "error" -> {
                                // 处理错误状态，只记录日志，不显示Toast
                                Log.i(TAG_NET, "服务器返回error状态: $answer")
                            }
                            else -> {
                                // 处理其他未知状态
                                Log.w(TAG_NET, "收到未知状态的消息: status=$messageStatus, answer=$answer, url=$url")
                            }
                        }
                    } else {
                        // 非message类型的事件
                        Log.i(TAG_NET, "收到非message类型的事件: $event, 数据: $data")
                    }
                } catch (e: Exception) {
                    Log.e(TAG_NET, "解析SSE事件失败", e)
                    e.printStackTrace()
                }
            }

            override fun onClosed(eventSource: EventSource) {
                Log.i(TAG_NET, "SSE连接已关闭")
                currentEventSource = null
                
                // 检查是否还有未播放的音频
                if (audioChunks.isNotEmpty() && !isPlaying) {
                    Log.i(TAG_NET, "SSE连接关闭，但还有未播放的音频片段，开始播放")
                    playAudioChunk(audioChunks[0])
                } else if (!isInterrupted) {
                    // 如果没有在播放状态，立即开始录音
                    if (!isPlaying) {
                        isProcessingRequest = false
                        
                        // 开始录音，更新UI状态为"录音中..."
                        Log.i(TAG_NET, "SSE连接关闭，立即开始录音")
                        updateUIState("录音中...", true, "SSE连接关闭，立即开始录音")
                        
                        // 立即开始录音
                        if (!isRecording) {
                            isRecording = true
                            minerva?.start()
                        }
                        
                        // 重置语音检测状态
                        isVoiceDetected = false
                        isProcessingVoice = false
                        
                        // 重新启动语音检测
                        try {
                            voiceDetector?.stopListening()
                            voiceDetector?.startListening(object : AudioStreamObserver {
                                override fun onVoiceStart() {
                                    Log.i(TAG_NET, "检测到语音开始")
                                }

                                override fun onVoiceEnd(silenceDuration: Long) {
                                    Log.i(TAG_NET, "检测到语音结束，持续时间：$silenceDuration ms")
                                    if (isRecording && !isPlaying && !isProcessingRequest) {
                                        isRecording = false
                                        minerva?.stop()
                                    }
                                }

                                override fun onError(e: Exception) {
                                    Log.e(TAG_NET, "语音检测错误", e)
                                }
                            })
                        } catch (e: Exception) {
                            Log.e(TAG_NET, "重启语音检测失败", e)
                        }
                        
                        // 添加：延迟隐藏建议问题
                        try {
                            // 延迟30秒后，如果仍未检测到有效语音，才隐藏建议问题
                            Handler(Looper.getMainLooper()).postDelayed({
                                // 仅当当前没有播放且没有处理请求时才隐藏
                                if (!isPlaying && !isProcessingRequest && !isFinishing && !isDestroyed) {
                                    Log.i(TAG_NET, "30秒内未检测到语音，隐藏建议问题")
                                    hideSuggestedQuestions()
                                }
                            }, 30000) // 30秒延迟
                        } catch (e: Exception) {
                            Log.e(TAG_NET, "设置延迟隐藏建议问题失败", e)
                        }
                    } else {
                        Log.i(TAG_NET, "SSE已关闭但正在播放，保持当前UI状态")
                    }
                }
            }

            override fun onFailure(eventSource: EventSource, t: Throwable?, response: Response?) {
                Log.e(TAG_NET, "SSE连接失败: ${t?.message}", t)
                currentEventSource = null
                // 如果没有在播放状态，延迟后开始录音
                if (!isPlaying) {
                    isProcessingRequest = false

                    // 延迟2秒后开始录音，避免频繁重试
                    Log.i(TAG_NET, "SSE连接失败，2秒后开始录音")
                    updateUIState("连接失败，稍后重试...", true, "SSE连接失败，延迟重试")

                    Handler(Looper.getMainLooper()).postDelayed({
                        if (!isPlaying && !isProcessingRequest && !isFinishing && !isDestroyed) {
                            Log.i(TAG_NET, "延迟后开始录音")
                            updateUIState("录音中...", true, "延迟后开始录音")

                            // 开始录音
                            if (!isRecording) {
                                isRecording = true
                                minerva?.start()
                            }
                        }
                    }, 2000) // 延迟2秒
                    
                    // 重置语音检测状态
                    isVoiceDetected = false
                    isProcessingVoice = false
                    
                    // 重新启动语音检测
                    try {
                        voiceDetector?.stopListening()
                        voiceDetector?.startListening(object : AudioStreamObserver {
                            override fun onVoiceStart() {
                                Log.i(TAG_NET, "检测到语音开始")
                            }

                            override fun onVoiceEnd(silenceDuration: Long) {
                                Log.i(TAG_NET, "检测到语音结束，持续时间：$silenceDuration ms")
                                if (isRecording && !isPlaying && !isProcessingRequest) {
                                    isRecording = false
                                    minerva?.stop()
                                }
                            }

                            override fun onError(e: Exception) {
                                Log.e(TAG_NET, "语音检测错误", e)
                            }
                        })
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "重启语音检测失败", e)
                    }
                } else {
                    Log.i(TAG_NET, "SSE连接失败但正在播放，保持当前UI状态")
                }
            }
        }

        // 启动SSE连接
        EventSources.createFactory(sseClient).newEventSource(request, listener)
    }

    // 下载音频片段
    private fun downloadAudioChunk(url: String): File? {
        Log.i(TAG_NET, "开始下载音频片段: $url")
        try {
            val request = Request.Builder()
                .url(url)
                .header("Accept", "audio/wav")
                .build()
            
            Log.i(TAG_NET, "发送下载请求: ${request.url}")
            val response = okHttpClient.newCall(request).execute()
            
            if (!response.isSuccessful) {
                Log.e(TAG_NET, "音频片段下载失败，状态码: ${response.code}, 响应头: ${response.headers}")
                return null
            }

            val audioDir = File(mContext.getExternalFilesDir("duix"), "audio_chunks")
            if (!audioDir.exists()) {
                audioDir.mkdirs()
            }

            val fileName = "chunk_${System.currentTimeMillis()}.wav"
            val localFile = File(audioDir, fileName)

            response.body?.let { responseBody ->
                val contentLength = responseBody.contentLength()
                Log.i(TAG_NET, "音频文件大小: $contentLength bytes")
                Log.i(TAG_NET, "Content-Type: ${response.header("Content-Type")}")
                Log.i(TAG_NET, "响应头: ${response.headers}")
                
                if (contentLength <= 0) {
                    Log.e(TAG_NET, "音频文件大小为0或无效")
                    return null
                }

                localFile.outputStream().use { output ->
                    responseBody.byteStream().use { input ->
                        val buffer = ByteArray(8192)
                        var bytesRead: Int
                        var totalBytesRead = 0L
                        
                        while (input.read(buffer).also { bytesRead = it } != -1) {
                            output.write(buffer, 0, bytesRead)
                            totalBytesRead += bytesRead
                            // 每读取1MB打印一次进度
                            if (totalBytesRead % (1024 * 1024) == 0L) {
                                Log.i(TAG_NET, "已下载: ${totalBytesRead / (1024 * 1024)}MB / ${contentLength / (1024 * 1024)}MB")
                            }
                        }
                        
                        Log.i(TAG_NET, "音频文件下载完成，总大小: $totalBytesRead bytes")
                    }
                }
                
                if (localFile.length() <= 0) {
                    Log.e(TAG_NET, "下载的音频文件大小为0")
                    return null
                }
                
                // 验证WAV文件头
                val fileHeader = ByteArray(44)
                localFile.inputStream().use { it.read(fileHeader) }
                Log.i(TAG_NET, "WAV文件头: ${fileHeader.joinToString { "%02X".format(it) }}")
                
                // 检查文件是否可读
                if (!localFile.canRead()) {
                    Log.e(TAG_NET, "下载的文件不可读")
                    return null
                }
                
                Log.i(TAG_NET, "音频片段已下载: ${localFile.absolutePath}, 大小: ${localFile.length()} bytes")
                return localFile
            }
            
            Log.e(TAG_NET, "响应体为空")
            return null
        } catch (e: Exception) {
            Log.e(TAG_NET, "下载音频片段出错", e)
            e.printStackTrace()
            return null
        }
    }

    // 播放音频片段
    private fun playAudioChunk(audioFile: File) {
        if (isInterrupted) {
            Log.i(TAG_NET, "播放已被打断，不继续播放")
            return
        }
        
        // 打印当前音频对应的问题和回答
        dumpAudioTextMappings(audioFile)
        
        // 检查DUIX SDK状态
        if (duix == null) {
            Log.e(TAG_NET, "DUIX SDK未初始化，无法播放音频")
            // 移除Toast
            return
        }
        
        // 确保所有录音状态被清除
        if (isRecording) {
            Log.i(TAG_NET, "播放前停止录音")
            isRecording = false
            minerva?.stop()
        }
        
        // 设置播放状态
        isPlaying = true
        currentChunkIndex = audioChunks.indexOf(audioFile)
        
        // 更新UI状态
        updateUIState("点击打断", true, "开始播放音频片段 #${currentChunkIndex+1}/${audioChunks.size}, 更新UI为打断状态")
        
        Log.i(TAG_NET, "准备播放音频片段: ${audioFile.absolutePath}, 大小: ${audioFile.length()} bytes")
        
        if (!audioFile.exists()) {
            Log.e(TAG_NET, "音频文件不存在: ${audioFile.absolutePath}")
            handlePlaybackError()
            return
        }
        
        if (audioFile.length() == 0L) {
            Log.e(TAG_NET, "音频文件大小为0: ${audioFile.absolutePath}")
            handlePlaybackError()
            return
        }
        
        if (!audioFile.canRead()) {
            Log.e(TAG_NET, "音频文件不可读: ${audioFile.absolutePath}")
            handlePlaybackError()
            return
        }
        
        try {
            // 验证WAV文件头
            val fileHeader = ByteArray(44)
            audioFile.inputStream().use { it.read(fileHeader) }
            Log.i(TAG_NET, "播放前WAV文件头: ${fileHeader.joinToString { "%02X".format(it) }}")
            
            // 播放音频
            Log.i(TAG_NET, "调用DUIX播放音频")
            duix?.playAudio(audioFile.absolutePath)
            Log.i(TAG_NET, "音频播放命令已发送")
        } catch (e: Exception) {
            Log.e(TAG_NET, "播放音频时出错", e)
            e.printStackTrace()
            handlePlaybackError()
        }
    }

    // 辅助方法：打印当前音频对应的问题和回答
    private fun dumpAudioTextMappings(currentAudio: File) {
        Log.i(TAG_NET, "===== 当前音频映射信息 =====")
        Log.i(TAG_NET, "音频文件: ${currentAudio.name}")
        Log.i(TAG_NET, "存储的问题: ${questionMap[currentAudio] ?: "无"}")
        Log.i(TAG_NET, "存储的回答: ${audioTextMap[currentAudio]?.take(50)}...")
        
        Log.i(TAG_NET, "===== 所有音频映射信息 =====")
        audioChunks.forEachIndexed { index, file ->
            Log.i(TAG_NET, "片段 #$index - ${file.name}")
            Log.i(TAG_NET, "  问题: ${questionMap[file] ?: "无"}")
            Log.i(TAG_NET, "  回答: ${audioTextMap[file]?.take(30)}...")
        }
        Log.i(TAG_NET, "=========================")
    }

    private fun handlePlaybackError() {
        // 如果文件有问题，尝试播放下一段
        val nextIndex = currentChunkIndex + 1
        if (nextIndex < audioChunks.size) {
            Log.i(TAG_NET, "当前音频文件无效，尝试播放下一段")
            playAudioChunk(audioChunks[nextIndex])
        } else {
            Log.i(TAG_NET, "所有音频已播放完毕")
            isPlaying = false
            isProcessingRequest = false
            
            // 开始录音，更新UI状态为"录音中..."
            runOnUiThread {
                updateUIState("录音中...", true, "播放错误，立即开始录音")
            }
            
            // 立即开始录音
            if (!isRecording) {
                isRecording = true
                minerva?.start()
            }
            
            // 重置语音检测状态
            isVoiceDetected = false
            isProcessingVoice = false
            
            // 重新启动语音检测
            try {
                voiceDetector?.stopListening()
                voiceDetector?.startListening(object : AudioStreamObserver {
                    override fun onVoiceStart() {
                        Log.i(TAG_NET, "检测到语音开始")
                    }

                    override fun onVoiceEnd(silenceDuration: Long) {
                        Log.i(TAG_NET, "检测到语音结束，持续时间：$silenceDuration ms")
                        if (isRecording && !isPlaying && !isProcessingRequest) {
                            isRecording = false
                            minerva?.stop()
                        }
                    }

                    override fun onError(e: Exception) {
                        Log.e(TAG_NET, "语音检测错误", e)
                    }
                })
            } catch (e: Exception) {
                Log.e(TAG_NET, "重启语音检测失败", e)
            }
        }
    }

    private fun checkRecordPermission(): Boolean {
        return ContextCompat.checkSelfPermission(
            this, 
            Manifest.permission.RECORD_AUDIO
        ) == PackageManager.PERMISSION_GRANTED
    }

    private fun requestRecordPermission() {
        ActivityCompat.requestPermissions(
            this, 
            arrayOf(Manifest.permission.RECORD_AUDIO), 
            PERMISSION_REQUEST_CODE
        )
    }

    override fun onResume() {
        super.onResume()
        Log.i(TAG_NET, "CallActivity onResume")
        
        // 检查当前状态
        Log.i(TAG_NET, "当前状态: isRecording=$isRecording, isPlaying=$isPlaying, isProcessingRequest=$isProcessingRequest")
        
        // 根据当前状态更新UI
        if (isPlaying) {
            // 如果正在播放，显示"点击打断"
            updateUIState("点击打断", true, "恢复状态: 正在播放中")
        } else if (isProcessingRequest) {
            // 如果正在处理请求，显示"处理中..."
            updateUIState("处理中...", false, "恢复状态: 正在处理请求")
        } else if (isRecording) {
            // 如果正在录音，显示"录音中..."
            updateUIState("录音中...", true, "恢复状态: 正在录音")
        } else {
            // 默认状态，显示"等待语音..."
            updateUIState("等待语音...", false, "恢复状态: 等待语音")
        }
        
        // 如果有权限但任一组件为null，初始化它
        if (checkRecordPermission()) {
            // 只有在minerva为null时才初始化
            if (minerva == null) {
                Log.i(TAG_NET, "onResume中初始化录音功能")
                initMinerva()
            }
            
            // 只有在voiceDetector为null时才初始化
            if (voiceDetector == null) {
                Log.i(TAG_NET, "onResume中初始化语音检测")
                initVoiceDetection()
            }
        }
    }
    
    override fun onPause() {
        super.onPause()
        Log.i(TAG_NET, "CallActivity onPause")
        
        // 立即停止所有任务
        Log.i(TAG_NET, "页面暂停，立即停止所有任务")
        
        // 1. 停止录音
        if (isRecording) {
            Log.i(TAG_NET, "停止录音")
            isRecording = false
            try {
                minerva?.stop()
            } catch (e: Exception) {
                Log.e(TAG_NET, "停止录音出错", e)
            }
        }
        
        // 2. 取消网络请求
        if (isProcessingRequest && currentEventSource != null) {
            Log.i(TAG_NET, "取消网络请求")
            isProcessingRequest = false
            try {
                currentEventSource?.cancel()
                currentEventSource = null
            } catch (e: Exception) {
                Log.e(TAG_NET, "取消网络请求出错", e)
            }
        }
        
        // 3. 停止语音检测（不销毁实例，仅暂停）
        try {
            Log.i(TAG_NET, "暂停语音检测")
            voiceDetector?.stopListening()
        } catch (e: Exception) {
            Log.e(TAG_NET, "停止语音检测出错", e)
        }
        
        // 4. 停止音频播放
        if (isPlaying) {
            Log.i(TAG_NET, "停止音频播放")
            isPlaying = false
            isInterrupted = true
            try {
                duix?.stopAudio()
            } catch (e: Exception) {
                Log.e(TAG_NET, "停止音频播放出错", e)
            }
        }
        
        // 5. 重置所有状态
        isRecording = false
        isProcessingRequest = false
        isVoiceDetected = false
        isProcessingVoice = false
        
        // 这里不清空voiceDetector实例，在onResume时会重新启动
    }

    // 添加一个安全更新UI状态的方法
    private fun updateUIState(uiState: String, enabled: Boolean, logMessage: String) {
        Log.i(TAG_NET, logMessage)
        Handler(Looper.getMainLooper()).post {
            try {
                if (!isFinishing && binding != null) {
                    // 不再在按钮上显示文本
                    binding?.btnRecord?.isEnabled = enabled
                    // 在状态文本控件上显示状态
                    binding?.recordingStatus?.text = uiState
                    
                    // 根据不同状态更改麦克风按钮的背景色
                    when (uiState) {
                        "点击打断" -> {
                            // 设置为红色背景
                            binding?.micButtonCard?.setCardBackgroundColor(resources.getColor(android.R.color.holo_red_light))
                            Log.i(TAG_NET, "麦克风按钮背景设为红色 - 打断状态")
                        }
                        "录音中..." -> {
                            // 为录音状态设置绿色背景
                            binding?.micButtonCard?.setCardBackgroundColor(resources.getColor(android.R.color.holo_green_light))
                            Log.i(TAG_NET, "麦克风按钮背景设为绿色 - 录音状态")
                            
                            // 设置定时器在1.5秒后切换到"请让我思考一下..."状态
                            Handler(Looper.getMainLooper()).postDelayed({
                                if (!isFinishing && binding != null && !isPlaying && isProcessingRequest) {
                                    binding?.recordingStatus?.text = "请让我思考一下..."
                                    Log.i(TAG_NET, "自动切换状态: 录音中... -> 请让我思考一下...")
                                }
                            }, 1500)
                        }
                        "请让我思考一下..." -> {
                            // 为思考状态设置绿色背景
                            binding?.micButtonCard?.setCardBackgroundColor(resources.getColor(android.R.color.holo_green_light))
                            Log.i(TAG_NET, "麦克风按钮背景设为绿色 - 思考状态")
                            
                            // 设置定时器在1.5秒后切换回"录音中..."状态
                            Handler(Looper.getMainLooper()).postDelayed({
                                if (!isFinishing && binding != null && !isPlaying && isProcessingRequest) {
                                    binding?.recordingStatus?.text = "录音中..."
                                    Log.i(TAG_NET, "自动切换状态: 请让我思考一下... -> 录音中...")
                                }
                            }, 1500)
                        }
                        "处理中..." -> {
                            // 为处理状态设置绿色背景
                            binding?.micButtonCard?.setCardBackgroundColor(resources.getColor(android.R.color.holo_green_light))
                            Log.i(TAG_NET, "麦克风按钮背景设为绿色 - 处理状态")
                        }
                        else -> {
                            // 初始化和等待语音状态使用蓝色背景
                            binding?.micButtonCard?.setCardBackgroundColor(resources.getColor(android.R.color.holo_blue_light))
                            Log.i(TAG_NET, "麦克风按钮背景设为蓝色 - ${uiState}状态")
                        }
                    }
                    
                    Log.i(TAG_NET, "UI状态已更新为: $uiState, isEnabled=$enabled")
                }
            } catch (e: Exception) {
                Log.e(TAG_NET, "更新UI状态失败", e)
            }
        }
    }

    private fun handleVoiceInterruption() {
        if (isPlaying && !isProcessingVoice) {
            Log.i(TAG_NET, "检测到语音结束，准备打断当前播放")
            isProcessingVoice = true
            
            // 延迟一小段时间再打断，确保语音完全结束
            Handler(Looper.getMainLooper()).postDelayed({
                if (isPlaying) {
                    Log.i(TAG_NET, "执行打断操作")
                    interruptPlayback()
                    
                    // 开始新的录音
                    if (!isRecording) {
                        isRecording = true
                        runOnUiThread {
                            binding?.btnRecord?.isEnabled = true
                            binding?.recordingStatus?.text = "录音中..."
                        }
                        minerva?.start()
                    }
                }
                isProcessingVoice = false
            }, 500) // 延迟500ms
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            PERMISSION_REQUEST_CODE -> {
                if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    // 只有在minerva为null时才初始化，避免重复初始化
                    if (minerva == null) {
                        initMinerva()
                    }
                    
                    // 只有在voiceDetector为null时才初始化
                    if (voiceDetector == null) {
                        initVoiceDetection()
                    }
                } else {
                    Log.e(TAG_NET, "录音权限被拒绝")
                    finish() // 无录音权限则关闭页面
                }
            }
        }
    }

    // 开始音频捕获
    private fun startAudioCapture() {
        if (isRecordingAudio) return

        try {
            val minBufferSize = AudioRecord.getMinBufferSize(
                16000,
                AudioFormat.CHANNEL_IN_MONO,
                AudioFormat.ENCODING_PCM_16BIT
            )

            audioRecord = AudioRecord(
                MediaRecorder.AudioSource.MIC,
                16000,
                AudioFormat.CHANNEL_IN_MONO,
                AudioFormat.ENCODING_PCM_16BIT,
                minBufferSize
            )

            audioRecord?.startRecording()
            isRecordingAudio = true

            // 开始音频处理线程
            audioThread.execute {
                val buffer = ShortArray(audioBufferSize)
                while (isRecordingAudio) {
                    val readSize = audioRecord?.read(buffer, 0, buffer.size) ?: 0
                    if (readSize > 0) {
                        // 检测声音
                        if (detectVoice(buffer, readSize)) {
                            val currentTime = System.currentTimeMillis()
                            if (currentTime - lastVoiceDetectedTime > voiceDetectionThreshold) {
                                lastVoiceDetectedTime = currentTime
                                // 在主线程中处理打断
                                runOnUiThread {
                                    handleVoiceInterruption()
                                }
                            }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG_NET, "音频捕获启动失败", e)
        }
    }

    // 停止音频捕获
    private fun stopAudioCapture() {
        isRecordingAudio = false
        try {
            audioRecord?.stop()
            audioRecord?.release()
            audioRecord = null
        } catch (e: Exception) {
            Log.e(TAG_NET, "停止音频捕获失败", e)
        }
    }

    // 检测声音
    private fun detectVoice(buffer: ShortArray, size: Int): Boolean {
        var sum = 0.0
        for (i in 0 until size) {
            sum += Math.abs(buffer[i].toDouble())
        }
        val average = sum / size
        val currentTime = System.currentTimeMillis()
        
        // 检测到声音
        if (average > 1000) { // 可以调整这个阈值
            if (!isVoiceDetected) {
                // 开始检测到声音
                isVoiceDetected = true
                voiceStartTime = currentTime
                lastVoiceTime = currentTime
                Log.i(TAG_NET, "开始检测到声音")
            } else {
                // 持续检测到声音
                lastVoiceTime = currentTime
            }
            return false // 不立即触发打断
        } else {
            // 没有检测到声音
            if (isVoiceDetected) {
                // 之前检测到声音，现在停止
                val silenceDuration = currentTime - lastVoiceTime
                val totalDuration = currentTime - voiceStartTime
                
                if (silenceDuration > silenceThreshold && totalDuration > minVoiceDuration) {
                    // 满足静音时长和最小语音时长要求
                    isVoiceDetected = false
                    Log.i(TAG_NET, "语音结束，持续时间: ${totalDuration}ms")
                    return true // 触发打断
                }
            }
            return false
        }
    }

    // 显示对话框中的问题和回答(带打字效果)
    private fun showDialogWithTexts(question: String, answer: String) {
        Log.i(TAG_NET, "显示问题和回答，问题：$question，回答：$answer")
        
        // 停止当前正在进行的打字效果
        typingHandler.removeCallbacksAndMessages(null)
        
        // 重置状态
        currentTypingPosition = 0
        fullAnswerText = answer
        
        // 先显示对话框和问题，但暂时不显示回答
        runOnUiThread {
            try {
                if (binding != null && !isFinishing && !isDestroyed) {
                    // 先显示对话容器
                    binding?.cvDialogContainer?.visibility = View.VISIBLE
                    
                    // 设置问题文本并确保问题容器可见
                    binding?.tvQuestion?.text = question
                    binding?.llQuestionContainer?.visibility = View.VISIBLE
                    
                    // 清空回答文本，准备打字效果
                    binding?.tvAnswer?.text = ""
                }
            } catch (e: Exception) {
                Log.e(TAG_NET, "显示对话文本失败", e)
            }
        }
        
        // 开始打字效果
        startTypingEffect()
    }
    
    // 只显示回答不显示问题
    private fun showDialogWithAnswer(answer: String) {
        Log.i(TAG_NET, "只显示回答，不显示问题：$answer")
        
        // 尝试使用全局问题文本
        if (!questionText.isNullOrEmpty()) {
            Log.i(TAG_NET, "发现全局问题文本，改为显示问题和回答")
            showDialogWithTexts(questionText!!, answer)
            return
        }
        
        // 停止当前正在进行的打字效果
        typingHandler.removeCallbacksAndMessages(null)
        
        // 重置状态
        currentTypingPosition = 0
        fullAnswerText = answer
        
        // 显示对话框和空白问题区域
        runOnUiThread {
            // 先显示对话容器
            binding?.cvDialogContainer?.visibility = View.VISIBLE
            
            // 隐藏问题容器
            binding?.llQuestionContainer?.visibility = View.GONE
            
            // 清空回答文本，准备打字效果
            binding?.tvAnswer?.text = ""
        }
        
        // 开始打字效果
        startTypingEffect()
    }
    
    // 添加显示问题文本的方法
    private fun showQuestionText(question: String) {
        runOnUiThread {
            binding?.tvQuestion?.text = question
        }
    }
    
    // 添加隐藏问答文本的方法
    private fun hideQuestionAnswerText() {
        Log.i(TAG_NET, "隐藏问答文本")
        
        // 停止所有文本相关的处理
        textHideHandler.removeCallbacksAndMessages(null)
        typingHandler.removeCallbacksAndMessages(null)
        
        runOnUiThread {
            try {
                if (binding != null && !isFinishing && !isDestroyed) {
                    // 隐藏整个对话容器
                    binding?.cvDialogContainer?.visibility = View.GONE
                    
                    // 重置问题容器的可见性，以便下次正确显示
                    binding?.llQuestionContainer?.visibility = View.VISIBLE
                }
            } catch (e: Exception) {
                Log.e(TAG_NET, "隐藏问答文本失败", e)
            }
            
            // 清空文本状态
            questionText = null
            answerText = null
            fullAnswerText = ""
            currentTypingPosition = 0
        }
    }
    
    // 计划在一段时间后隐藏文本
    private fun scheduleHideText() {
        textHideHandler.removeCallbacksAndMessages(null) // 移除之前的任务
        textHideHandler.postDelayed({
            hideQuestionAnswerText()
        }, TEXT_DISPLAY_DURATION)
    }

    // 执行打字效果
    private fun startTypingEffect() {
        Log.i(TAG_NET, "开始显示打字效果，总长度: ${fullAnswerText.length}")
        currentTypingPosition = 0
        
        // 计算适合的打字速度 - 长文本使用更快的速度
        val baseDelay = when {
            fullAnswerText.length > 200 -> 8L // 长文本用更快的速度
            fullAnswerText.length > 100 -> 12L // 中等长度文本
            else -> TYPING_DELAY // 短文本用默认速度
        }
        
        // 定义递归显示打字的函数
        fun showNextChar() {
            if (isFinishing || isDestroyed) return
            
            if (currentTypingPosition < fullAnswerText.length) {
                val currentChar = if (currentTypingPosition < fullAnswerText.length) {
                    fullAnswerText[currentTypingPosition]
                } else ' '
                
                // 增加位置
                currentTypingPosition++
                
                // 根据字符类型动态调整下一个字符的延迟
                val extraDelay = when (currentChar) {
                    '，', ',' -> 150L // 逗号稍微停顿
                    '。', '.' -> 300L // 句号多停顿
                    '；', ';' -> 200L // 分号停顿
                    '！', '!' -> 250L // 感叹号停顿
                    '?', '？' -> 250L // 问号停顿
                    '\n' -> 350L      // 换行多停顿
                    else -> 0L        // 其他字符正常速度
                }
                
                // 更新显示的文本
                val currentText = fullAnswerText.substring(0, currentTypingPosition)
                runOnUiThread {
                    try {
                        binding?.tvAnswer?.text = currentText
                        
                        // 确保ScrollView滚动到最新位置
                        binding?.tvAnswer?.parent?.let { parent ->
                            if (parent is ScrollView) {
                                parent.post {
                                    parent.fullScroll(ScrollView.FOCUS_DOWN)
                                }
                            }
                        }
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "更新打字文本失败", e)
                    }
                }
                
                // 设置下一个字符的显示
                typingHandler.postDelayed({ showNextChar() }, baseDelay + extraDelay)
            } else {
                // 打字效果结束
                Log.i(TAG_NET, "打字效果显示完成")
            }
        }
        
        // 开始显示第一个字符
        typingHandler.post { showNextChar() }
    }
    
    // 添加显示回答文本的方法（立即显示，不使用打字效果）
    private fun showAnswerText(answer: String) {
        runOnUiThread {
            binding?.tvAnswer?.text = answer
            binding?.cvDialogContainer?.visibility = View.VISIBLE
        }
    }

    // 显示建议问题
    private fun showSuggestedQuestions() {
        Log.i(TAG_NET, "显示建议问题: $currentSuggestedQuestions")
        
        // 检查当前是否有建议问题
        if (currentSuggestedQuestions.isEmpty()) {
            Log.i(TAG_NET, "没有建议问题可显示")
            runOnUiThread {
                binding?.suggestionContainer?.visibility = View.GONE
            }
            return
        }
        
        runOnUiThread {
            try {
                // 设置建议问题文本
                if (currentSuggestedQuestions.size >= 1) {
                    binding?.suggestion1?.text = currentSuggestedQuestions[0]
                    binding?.suggestion1?.visibility = View.VISIBLE
                    binding?.suggestionCard1?.visibility = View.VISIBLE
                    Log.i(TAG_NET, "显示建议问题1: ${currentSuggestedQuestions[0]}")
                } else {
                    binding?.suggestion1?.visibility = View.GONE
                    binding?.suggestionCard1?.visibility = View.GONE
                }
                
                if (currentSuggestedQuestions.size >= 2) {
                    binding?.suggestion2?.text = currentSuggestedQuestions[1]
                    binding?.suggestion2?.visibility = View.VISIBLE
                    binding?.suggestionCard2?.visibility = View.VISIBLE
                    Log.i(TAG_NET, "显示建议问题2: ${currentSuggestedQuestions[1]}")
                } else {
                    binding?.suggestion2?.visibility = View.GONE
                    binding?.suggestionCard2?.visibility = View.GONE
                }
                
                if (currentSuggestedQuestions.size >= 3) {
                    binding?.suggestion3?.text = currentSuggestedQuestions[2]
                    binding?.suggestion3?.visibility = View.VISIBLE
                    binding?.suggestionCard3?.visibility = View.VISIBLE
                    Log.i(TAG_NET, "显示建议问题3: ${currentSuggestedQuestions[2]}")
                } else {
                    binding?.suggestion3?.visibility = View.GONE
                    binding?.suggestionCard3?.visibility = View.GONE
                }
                
                // 测试提示，仅在开发环境显示
                // Toast.makeText(this@CallActivity, "显示了${currentSuggestedQuestions.size}个建议问题", Toast.LENGTH_SHORT).show()
                
                // 先将透明度设为0
                binding?.suggestionContainer?.alpha = 0f
                // 显示建议问题容器
                binding?.suggestionContainer?.visibility = View.VISIBLE
                
                // 添加淡入动画效果
                binding?.suggestionContainer?.animate()
                    ?.alpha(1f)
                    ?.setDuration(500)
                    ?.start()
            } catch (e: Exception) {
                Log.e(TAG_NET, "显示建议问题时出错", e)
                // 出错时尝试显示基本视图
                try {
                    binding?.suggestionContainer?.visibility = View.VISIBLE
                } catch (e2: Exception) {
                    Log.e(TAG_NET, "显示建议问题容器失败", e2)
                }
            }
        }
    }
    
    // 隐藏建议问题
    private fun hideSuggestedQuestions() {
        Log.i(TAG_NET, "隐藏建议问题")
        
        runOnUiThread {
            try {
                if (binding != null && !isFinishing && !isDestroyed) {
                    // 添加淡出动画效果
                    binding?.suggestionContainer?.animate()
                        ?.alpha(0f)
                        ?.setDuration(300)
                        ?.withEndAction {
                            try {
                                binding?.suggestionContainer?.visibility = View.GONE
                            } catch (e: Exception) {
                                Log.e(TAG_NET, "设置建议问题容器为GONE失败", e)
                            }
                        }
                        ?.start()
                }
            } catch (e: Exception) {
                Log.e(TAG_NET, "隐藏建议问题失败", e)
                // 尝试直接设置可见性
                try {
                    if (binding != null) {
                        binding?.suggestionContainer?.visibility = View.GONE
                    }
                } catch (e2: Exception) {
                    Log.e(TAG_NET, "直接设置建议问题容器可见性失败", e2)
                }
            }
        }
    }
    
    // 点击建议问题时触发
    private fun askSuggestedQuestion(question: String) {
        Log.i(TAG_NET, "用户点击了建议问题: $question")
        
        // 设置对话已开始标志
        hasConversationStarted = true
        
        // 找到被点击的TextView并设置高亮背景
        val clickedTextView = when {
            currentSuggestedQuestions.isNotEmpty() && currentSuggestedQuestions[0] == question -> binding?.suggestion1
            currentSuggestedQuestions.size >= 2 && currentSuggestedQuestions[1] == question -> binding?.suggestion2
            currentSuggestedQuestions.size >= 3 && currentSuggestedQuestions[2] == question -> binding?.suggestion3
            else -> null
        }
        
        // 应用高亮效果
        runOnUiThread {
            clickedTextView?.setBackgroundResource(ai.guiji.duix.test.R.drawable.bg_suggestion_item_selected)
            
            // 添加短暂延迟后再发送问题，让用户能看到点击效果
            clickedTextView?.postDelayed({
                // 如果当前正在播放，先停止播放
                if (isPlaying) {
                    Log.i(TAG_NET, "点击建议问题时停止当前播放")
                    try {
                        // 强制停止当前音频播放
                        if (duix != null) {
                            duix?.stopAudio()
                            Log.i(TAG_NET, "DUIX停止音频成功")
                        }
                        
                        // 设置为结束状态，强制停止后续播放
                        currentChunkIndex = audioChunks.size
                        isPlaying = false
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "停止音频播放失败", e)
                    }
                }
                
                // 修改：延迟隐藏当前对话，让用户有时间阅读之前的内容
                if (binding?.cvDialogContainer?.visibility == View.VISIBLE) {
                    // 如果当前有显示的对话，先标记为文本保留状态
                    isTextPreserved = true
                    // 延迟2秒后再隐藏对话和建议问题
                    Handler(Looper.getMainLooper()).postDelayed({
                        if (!isFinishing && !isDestroyed) {
                            hideQuestionAnswerText()
                            hideSuggestedQuestions()
                            isTextPreserved = false
                            Log.i(TAG_NET, "延迟隐藏对话文本完成")
                        }
                    }, 2000) // 2秒后隐藏
                } else {
                    // 如果当前没有显示对话，直接隐藏
                    hideQuestionAnswerText()
                    hideSuggestedQuestions()
                }
                
                // 停止当前正在进行的录音
                if (isRecording) {
                    isRecording = false
                    minerva?.stop()
                }
                
                // 开始处理新的问题
                isProcessingRequest = true
                runOnUiThread {
                    binding?.btnRecord?.isEnabled = false
                    binding?.recordingStatus?.text = "请让我思考一下..."
                    // 更新UI颜色为绿色 - 表示正在处理中
                    binding?.micButtonCard?.setCardBackgroundColor(resources.getColor(android.R.color.holo_green_light))
                }
                
                // 保存问题文本
                questionText = question
                
                // 发送问题到/llm-streaming接口
                sendStreamingRequest(question)
                
                // 恢复原来的背景
                clickedTextView.setBackgroundResource(ai.guiji.duix.test.R.drawable.bg_suggestion_item)
            }, 200) // 延迟200毫秒
        }
    }
    
    // 发送问题到/llm-streaming接口
    private fun sendStreamingRequest(question: String) {
        // 检查请求频率
        if (!canSendRequest()) {
            Log.i(TAG_NET, "建议问题请求被防抖机制阻止")
            runOnUiThread {
                Toast.makeText(this, "请求过于频繁，请稍后再试", Toast.LENGTH_SHORT).show()
            }
            return
        }

        lastRequestTime = System.currentTimeMillis()
        isProcessingRequest = true
        isInterrupted = false
        currentChunkIndex = 0
        audioChunks.clear()
        
        // 直接设置问题文本，因为这是用户主动点击的建议问题
        questionText = question
        hasValidResponse = false // 重置标志，等待收到有效回答
        Log.i(TAG_NET, "设置问题文本(建议问题): $question")
        
        runOnUiThread {
            binding?.btnRecord?.isEnabled = false
            binding?.recordingStatus?.text = "请让我思考一下..."
        }

        try {
            // 准备请求URL - 使用/llm-streaming接口，增强URL构建的健壮性
            val baseUrl = if (ttsUrl.contains("/tts")) {
                ttsUrl.substringBefore("/tts")
            } else {
                // 如果没有/tts路径，则使用原始URL的域名和协议
                val uri = java.net.URI(ttsUrl)
                "${uri.scheme}://${uri.host}${if (uri.port != -1) ":${uri.port}" else ""}"
            }
            
            // 对问题文本进行URL编码
            val encodedQuestion = java.net.URLEncoder.encode(question, "UTF-8")
            val streamingUrl = "$baseUrl/llm-streaming?text=$encodedQuestion"
            
            // 打印请求信息
            Log.i(TAG_NET, "开始发送流式请求")
            LogUtils.getInstance(this).log("请求URL: $streamingUrl")
            Log.i(TAG_NET, "流式请求URL: $streamingUrl")
            Log.i(TAG_NET, "问题文本: $question")
            Log.i(TAG_NET, "使用音色: $reference_id")
            val deviceId = DeviceIdUtil.getDeviceId(this)
            
            // 检查DUIX SDK状态
            if (duix == null) {
                Log.e(TAG_NET, "DUIX SDK未初始化")
                // 移除Toast
                resetRequestState()
                return
            }
            
            // 创建请求构建器
            val requestBuilder = Request.Builder()
                .url(streamingUrl)
                .get()  // 使用GET方法

            // 添加请求头
            requestBuilder.addHeader("DIFY_API_KEY", apiKey)
            requestBuilder.addHeader("REFERENCE_ID", reference_id ?: "")
            requestBuilder.addHeader("USER_ID", deviceId)
            requestBuilder.addHeader("Accept", "text/event-stream") // 添加SSE支持的Header

            val request = requestBuilder.build()

            // 创建支持SSE的client
            val sseClient = okHttpClient.newBuilder()
                .readTimeout(0, TimeUnit.MILLISECONDS) // SSE需要无限超时
                .build()

            // 使用与现有逻辑相同的事件监听器
            val listener = object : EventSourceListener() {
                override fun onOpen(eventSource: EventSource, response: Response) {
                    Log.i(TAG_NET, "SSE连接已打开: ${response.code}")
                    currentEventSource = eventSource
                    runOnUiThread {
                        binding?.recordingStatus?.text = "请让我思考一下..."
                        binding?.btnRecord?.isEnabled = false
                    }
                }

                override fun onEvent(eventSource: EventSource, id: String?, type: String?, data: String) {
                    // 使用与sendTextToServer相同的事件处理逻辑
                    Log.i(TAG_NET, "收到SSE事件: $data")
                    if (isInterrupted) {
                        Log.i(TAG_NET, "已被打断，忽略SSE事件")
                        return
                    }

                    try {
                        val jsonNode = objectMapper.readTree(data)
                        val event = jsonNode.get("event")?.asText()
                        val status = jsonNode.get("status")?.asText()
                        
                        Log.i(TAG_NET, "解析事件: event=$event, status=$status")
                        
                        // 修改建议问题的处理逻辑
                        // 如果对话已经开始(一定是在这个方法中)，且存在data字段，优先使用data字段
                        if (jsonNode.has("data") && jsonNode.get("data")?.isArray == true) {
                            // 优先处理data格式的建议问题
                            val dataNode = jsonNode.get("data")
                            currentSuggestedQuestions.clear()
                            for (i in 0 until dataNode.size()) {
                                val suggestion = dataNode[i].asText()
                                if (suggestion != null && suggestion.isNotEmpty()) {
                                    currentSuggestedQuestions.add(suggestion)
                                }
                            }
                            Log.i(TAG_NET, "收到data格式的建议问题(会话中): $currentSuggestedQuestions")
                            
                            // 如果当前已经播放完毕，立即显示建议问题
                            if (!isPlaying && !isProcessingRequest) {
                                showSuggestedQuestions()
                            }
                        }
                        // 如果没有data字段但有根级别的suggested_questions，且对话未开始，才使用suggested_questions
                        else if (jsonNode.has("suggested_questions") && jsonNode.get("suggested_questions")?.isArray == true && !hasConversationStarted) {
                            // 处理根级别的suggested_questions
                            val suggestionsNode = jsonNode.get("suggested_questions")
                            if (suggestionsNode != null) {
                                currentSuggestedQuestions.clear()
                                for (i in 0 until suggestionsNode.size()) {
                                    val suggestion = suggestionsNode[i].asText()
                                    if (suggestion != null && suggestion.isNotEmpty()) {
                                        currentSuggestedQuestions.add(suggestion)
                                    }
                                }
                                Log.i(TAG_NET, "收到根级别的建议问题(初始化): $currentSuggestedQuestions")
                                
                                // 如果当前已经播放完毕，立即显示建议问题
                                if (!isPlaying && !isProcessingRequest) {
                                    showSuggestedQuestions()
                                }
                            }
                        }
                        // 如果上述两个字段都没有，但是event是parameters，则检查参数中的suggested_questions
                        else if (event == "parameters") {
                            // 只有在对话未开始时才处理parameters事件中的suggested_questions
                            if (!hasConversationStarted) {
                                val suggestionsNode = jsonNode.get("suggested_questions")
                                if (suggestionsNode != null && suggestionsNode.isArray) {
                                    currentSuggestedQuestions.clear()
                                    for (i in 0 until suggestionsNode.size()) {
                                        val suggestion = suggestionsNode[i].asText()
                                        if (suggestion != null && suggestion.isNotEmpty()) {
                                            currentSuggestedQuestions.add(suggestion)
                                        }
                                    }
                                    Log.i(TAG_NET, "收到parameters中的建议问题(初始化): $currentSuggestedQuestions")
                                    
                                    // 显示建议问题
                                    showSuggestedQuestions()
                                }
                                
                                // 处理欢迎语
                                val openingStatement = jsonNode.get("opening_statement")?.asText()
                                if (openingStatement != null && openingStatement.isNotEmpty()) {
                                    Log.i(TAG_NET, "收到欢迎语: $openingStatement")
                                    // 显示欢迎语
                                    showWelcomeMessage(openingStatement)
                                }
                            } else {
                                Log.i(TAG_NET, "对话已开始，忽略parameters中的建议问题")
                            }
                        } else if (event == "message") {
                            val url = jsonNode.get("url")?.asText()
                            val answer = jsonNode.get("answer")?.asText()
                            val messageStatus = jsonNode.get("status")?.asText()
                            val question = jsonNode.get("question")?.asText()
                            
                            // 在处理message事件时，设置对话已开始标志
                            if ((question != null && question.isNotEmpty()) || 
                                (answer != null && answer.isNotEmpty())) {
                                hasConversationStarted = true
                            }
                            
                            // 添加：只有在满足特定条件时更新问题文本
                            // 当收到answer和question一起来时，且answer不为空，说明这是一个有效的问答对
                            if (question != null && question.isNotEmpty() && 
                                answer != null && answer.isNotEmpty()) {
                                hasValidResponse = true // 收到有效回答
                                questionText = question
                                Log.i(TAG_NET, "收到有效问答对，更新问题文本: $question")
                            }
                            
                            // 尝试解析消息中的建议问题，即便对话已开始，message中的建议问题也可能有效
                            val suggestionsNode = jsonNode.get("suggested_questions")
                            if (suggestionsNode != null && suggestionsNode.isArray) {
                                currentSuggestedQuestions.clear()
                                for (i in 0 until suggestionsNode.size()) {
                                    val suggestion = suggestionsNode[i].asText()
                                    if (suggestion != null && suggestion.isNotEmpty()) {
                                        currentSuggestedQuestions.add(suggestion)
                                    }
                                }
                                Log.i(TAG_NET, "收到message中的建议问题: $currentSuggestedQuestions")
                            }
                            
                            Log.i(TAG_NET, "解析消息内容: url=$url, answer=$answer, status=$messageStatus, question=$question")
                            
                            // 处理不同状态的消息
                            when (messageStatus) {
                                "ready" -> {
                                    // ready状态通常是服务器准备好了，但还没有生成音频
                                    if (question != null && question.isNotEmpty()) {
                                        Log.i(TAG_NET, "服务器已准备好处理请求: $question")
                                        // 修改：只在第一次保存问题文本，或者与有效回答配对时才更新
                                        if (questionText == null || (answer != null && answer.isNotEmpty())) {
                                            questionText = question
                                            Log.i(TAG_NET, "保存问题文本到全局变量: $question")
                                        } else {
                                            Log.i(TAG_NET, "忽略无关的问题文本更新: $question, 保留原问题: $questionText")
                                        }
                                    } else {
                                        Log.i(TAG_NET, "服务器已准备好处理请求")
                                    }
                                    // 这种状态下URL可能为空，这是正常的
                                    if (url == null || url.isEmpty()) {
                                        Log.i(TAG_NET, "ready状态下URL为空是正常的，等待后续音频URL")
                                    }
                                }
                                "ok" -> {
                                    if (url != null && url.isNotEmpty()) {
                                        // 正常处理带有URL的消息
                                        if (question != null && question.isNotEmpty()) {
                                            Log.i(TAG_NET, "收到对问题的回答: $answer")
                                            // 添加：只有当回答不为空时，才更新问题文本
                                            if (answer != null && answer.isNotEmpty()) {
                                                questionText = question
                                                Log.i(TAG_NET, "更新问题文本(带有回答): $question")
                                            }
                                        } else {
                                            Log.i(TAG_NET, "收到随机回答: $answer")
                                        }
                                        
                                        // 直接在主线程中下载音频
                                        try {
                                            Log.i(TAG_NET, "开始下载音频: $url")
                                            val audioChunk = downloadAudioChunk(url)
                                            if (audioChunk != null) {
                                                // 将音频文件与问题和回答文本关联
                                                if (answer != null && answer.isNotEmpty()) {
                                                    audioTextMap[audioChunk] = answer
                                                    Log.i(TAG_NET, "存储回答文本: ${answer.take(20)}...")
                                                }
                                                if (question != null && question.isNotEmpty() && answer != null && answer.isNotEmpty()) {
                                                    // 修改：只有当有回答时，才使用当前消息的问题存储
                                                    questionMap[audioChunk] = question
                                                    Log.i(TAG_NET, "存储问题文本(当前消息): ${question.take(20)}...")
                                                } else if (questionText != null && questionText!!.isNotEmpty()) {
                                                    // 如果当前消息没有问题或没有回答，但之前有保存的问题，使用之前的问题
                                                    questionMap[audioChunk] = questionText!!
                                                    Log.i(TAG_NET, "使用之前保存的问题文本: ${questionText?.take(20)}...")
                                                }
                                                
                                                audioChunks.add(audioChunk)
                                                Log.i(TAG_NET, "音频片段已添加到队列，当前队列大小: ${audioChunks.size}")
                                                
                                                // 如果是第一个片段，立即播放
                                                if (audioChunks.size == 1 && !isInterrupted) {
                                                    Log.i(TAG_NET, "准备播放第一个音频片段")
                                                    playAudioChunk(audioChunk)
                                                }
                                            } else {
                                                Log.e(TAG_NET, "音频片段下载失败，URL: $url")
                                            }
                                        } catch (e: Exception) {
                                            Log.e(TAG_NET, "下载音频片段失败: ${e.message}", e)
                                            e.printStackTrace()
                                        }
                                    } else {
                                        Log.w(TAG_NET, "状态为ok但URL为空")
                                        // 尝试从其他字段获取有用信息
                                        if (answer != null && answer.isNotEmpty()) {
                                            Log.i(TAG_NET, "收到回答但无音频: $answer")
                                        }
                                    }
                                }
                                "error" -> {
                                    // 处理错误状态，只记录日志，不显示Toast
                                    Log.i(TAG_NET, "服务器返回error状态: $answer")
                                }
                                else -> {
                                    // 处理其他未知状态
                                    Log.w(TAG_NET, "收到未知状态的消息: status=$messageStatus, answer=$answer, url=$url")
                                }
                            }
                        } else {
                            // 非message类型的事件
                            Log.i(TAG_NET, "收到非message类型的事件: $event, 数据: $data")
                        }
                    } catch (e: Exception) {
                        Log.e(TAG_NET, "解析SSE事件失败", e)
                        e.printStackTrace()
                    }
                }

                override fun onClosed(eventSource: EventSource) {
                    Log.i(TAG_NET, "SSE连接已关闭")
                    currentEventSource = null
                    
                    // 检查是否还有未播放的音频
                    if (audioChunks.isNotEmpty() && !isPlaying) {
                        Log.i(TAG_NET, "SSE连接关闭，但还有未播放的音频片段，开始播放")
                        playAudioChunk(audioChunks[0])
                    } else if (!isInterrupted) {
                        // 如果没有在播放状态，立即开始录音
                        if (!isPlaying) {
                            isProcessingRequest = false
                            
                            // 开始录音，更新UI状态为"录音中..."
                            Log.i(TAG_NET, "SSE连接关闭，立即开始录音")
                            updateUIState("录音中...", true, "SSE连接关闭，立即开始录音")
                            
                            // 立即开始录音
                            if (!isRecording) {
                                isRecording = true
                                minerva?.start()
                            }
                            
                            // 重置语音检测状态
                            isVoiceDetected = false
                            isProcessingVoice = false
                            
                            // 重新启动语音检测
                            try {
                                voiceDetector?.stopListening()
                                voiceDetector?.startListening(object : AudioStreamObserver {
                                    override fun onVoiceStart() {
                                        Log.i(TAG_NET, "检测到语音开始")
                                    }

                                    override fun onVoiceEnd(silenceDuration: Long) {
                                        Log.i(TAG_NET, "检测到语音结束，持续时间：$silenceDuration ms")
                                        if (isRecording && !isPlaying && !isProcessingRequest) {
                                            isRecording = false
                                            minerva?.stop()
                                        }
                                    }

                                    override fun onError(e: Exception) {
                                        Log.e(TAG_NET, "语音检测错误", e)
                                    }
                                })
                            } catch (e: Exception) {
                                Log.e(TAG_NET, "重启语音检测失败", e)
                            }
                            
                            // 添加：延迟隐藏建议问题
                            try {
                                // 延迟30秒后，如果仍未检测到有效语音，才隐藏建议问题
                                Handler(Looper.getMainLooper()).postDelayed({
                                    // 仅当当前没有播放且没有处理请求时才隐藏
                                    if (!isPlaying && !isProcessingRequest && !isFinishing && !isDestroyed) {
                                        Log.i(TAG_NET, "30秒内未检测到语音，隐藏建议问题")
                                        hideSuggestedQuestions()
                                    }
                                }, 30000) // 30秒延迟
                            } catch (e: Exception) {
                                Log.e(TAG_NET, "设置延迟隐藏建议问题失败", e)
                            }
                        } else {
                            Log.i(TAG_NET, "SSE已关闭但正在播放，保持当前UI状态")
                        }
                    }
                }

                override fun onFailure(eventSource: EventSource, t: Throwable?, response: Response?) {
                    Log.e(TAG_NET, "SSE连接失败: ${t?.message}", t)
                    currentEventSource = null
                    // 如果没有在播放状态，延迟后开始录音
                    if (!isPlaying) {
                        isProcessingRequest = false

                        // 延迟2秒后开始录音，避免频繁重试
                        Log.i(TAG_NET, "SSE连接失败，2秒后开始录音")
                        updateUIState("连接失败，稍后重试...", true, "SSE连接失败，延迟重试")

                        Handler(Looper.getMainLooper()).postDelayed({
                            if (!isPlaying && !isProcessingRequest && !isFinishing && !isDestroyed) {
                                Log.i(TAG_NET, "延迟后开始录音")
                                updateUIState("录音中...", true, "延迟后开始录音")

                                // 开始录音
                                if (!isRecording) {
                                    isRecording = true
                                    minerva?.start()
                                }
                            }
                        }, 2000) // 延迟2秒
                        
                        // 重置语音检测状态
                        isVoiceDetected = false
                        isProcessingVoice = false
                        
                        // 重新启动语音检测
                        try {
                            voiceDetector?.stopListening()
                            voiceDetector?.startListening(object : AudioStreamObserver {
                                override fun onVoiceStart() {
                                    Log.i(TAG_NET, "检测到语音开始")
                                }

                                override fun onVoiceEnd(silenceDuration: Long) {
                                    Log.i(TAG_NET, "检测到语音结束，持续时间：$silenceDuration ms")
                                    if (isRecording && !isPlaying && !isProcessingRequest) {
                                        isRecording = false
                                        minerva?.stop()
                                    }
                                }

                                override fun onError(e: Exception) {
                                    Log.e(TAG_NET, "语音检测错误", e)
                                }
                            })
                        } catch (e: Exception) {
                            Log.e(TAG_NET, "重启语音检测失败", e)
                        }
                    } else {
                        Log.i(TAG_NET, "SSE连接失败但正在播放，保持当前UI状态")
                    }
                }
            }

            // 启动SSE连接
            EventSources.createFactory(sseClient).newEventSource(request, listener)
        } catch (e: Exception) {
            // 捕获所有可能的异常
            Log.e(TAG_NET, "流式请求初始化失败", e)
            e.printStackTrace()
            // 移除Toast
            resetRequestState()
        }
    }

    // 重置请求状态的辅助方法
    private fun resetRequestState() {
        isProcessingRequest = false
        
        // 更新UI状态
        runOnUiThread {
            binding?.btnRecord?.isEnabled = true
            binding?.recordingStatus?.text = "录音中..."
        }
        
        // 开始录音
        if (!isRecording) {
            isRecording = true
            minerva?.start()
        }
    }

    // 修改现有方法以清除按钮文本
    private fun addTextUpdatesToBtnRecord() {
        // 确保按钮没有文本
        binding?.btnRecord?.text = ""
    }
}
