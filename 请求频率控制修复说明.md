# 请求频率控制修复说明

## 问题分析

根据日志分析，发现以下问题：

1. **SSE连接频繁失败**：`unexpected end of stream on http://192.168.2.89:6600/v2/tts`
2. **请求过于频繁**：短时间内多次触发"Minerva开始录音"
3. **缺乏防抖机制**：SSE连接失败后立即重试，没有冷却时间

## 修复方案

### 1. 添加请求频率控制变量

```kotlin
private var lastRequestTime = 0L
private val requestCooldownTime = 3000L // 请求冷却时间：3秒
```

### 2. 实现防抖检查方法

```kotlin
// 检查是否可以发送请求（防抖控制）
private fun canSendRequest(): Boolean {
    val currentTime = System.currentTimeMillis()
    if (currentTime - lastRequestTime < requestCooldownTime) {
        Log.i(TAG_NET, "请求过于频繁，跳过本次请求。距离上次请求：${currentTime - lastRequestTime}ms")
        return false
    }
    return true
}
```

### 3. 修改音频发送逻辑

在 `sendAudioToServer()` 方法中添加频率检查：

```kotlin
private fun sendAudioToServer(audioFile: File) {
    // 检查请求频率
    if (!canSendRequest()) {
        Log.i(TAG_NET, "请求被防抖机制阻止，继续录音状态")
        // 继续保持录音状态，不发送请求
        runOnUiThread {
            updateUIState("录音中...", true, "请求被防抖阻止，继续录音")
        }
        return
    }
    
    lastRequestTime = System.currentTimeMillis()
    // ... 原有逻辑
}
```

### 4. 修改SSE连接失败处理

将立即重试改为延迟重试：

```kotlin
override fun onFailure(eventSource: EventSource, t: Throwable?, response: Response?) {
    Log.e(TAG_NET, "SSE连接失败: ${t?.message}", t)
    currentEventSource = null
    // 如果没有在播放状态，延迟后开始录音
    if (!isPlaying) {
        isProcessingRequest = false
        
        // 延迟2秒后开始录音，避免频繁重试
        Log.i(TAG_NET, "SSE连接失败，2秒后开始录音")
        updateUIState("连接失败，稍后重试...", true, "SSE连接失败，延迟重试")
        
        Handler(Looper.getMainLooper()).postDelayed({
            if (!isPlaying && !isProcessingRequest && !isFinishing && !isDestroyed) {
                Log.i(TAG_NET, "延迟后开始录音")
                updateUIState("录音中...", true, "延迟后开始录音")
                
                // 开始录音
                if (!isRecording) {
                    isRecording = true
                    minerva?.start()
                }
            }
        }, 2000) // 延迟2秒
    }
}
```

### 5. 为建议问题点击添加频率控制

在 `sendStreamingRequest()` 方法中添加频率检查：

```kotlin
private fun sendStreamingRequest(question: String) {
    // 检查请求频率
    if (!canSendRequest()) {
        Log.i(TAG_NET, "建议问题请求被防抖机制阻止")
        runOnUiThread {
            Toast.makeText(this, "请求过于频繁，请稍后再试", Toast.LENGTH_SHORT).show()
        }
        return
    }
    
    lastRequestTime = System.currentTimeMillis()
    // ... 原有逻辑
}
```

## 修复效果

### 预期改善

1. **减少频繁请求**：3秒内只允许一次请求
2. **优雅的错误处理**：连接失败后延迟2秒重试，而不是立即重试
3. **用户体验改善**：显示"连接失败，稍后重试..."提示用户当前状态
4. **防止重复点击**：建议问题按钮有防抖保护

### 日志改善

修复后的日志应该显示：
- 请求间隔至少3秒
- SSE连接失败后有2秒延迟
- 防抖机制生效的提示信息

## 使用建议

1. **监控日志**：观察是否还有频繁请求的情况
2. **调整参数**：如果3秒冷却时间过长，可以调整为2秒
3. **网络优化**：检查服务器端是否有连接数限制或超时设置
4. **错误处理**：考虑添加重试次数限制，避免无限重试

## 相关文件

- `test/src/main/java/ai/guiji/duix/test/ui/activity/CallActivity.kt`

## 测试验证

1. 快速连续说话，观察是否有防抖效果
2. 模拟网络不稳定情况，检查重试机制
3. 快速点击建议问题，验证防抖保护
4. 观察日志中的请求间隔时间
