<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="12dp">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:gravity="center"
        android:text="@string/app_name"
        android:textColor="@color/black"
        android:textSize="18dp"
        android:textStyle="italic|bold"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_sdk_version"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_title" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_sdk_version">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/tv_base_config_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:text="@string/base_config_title"
                android:textSize="13sp"

                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <Button
                android:id="@+id/btn_base_config_download"
                android:layout_width="130dp"
                android:layout_height="48dp"
                android:enabled="false"
                android:text="@string/download"
                android:textColor="@color/black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_base_config_title" />

            <ProgressBar
                android:id="@+id/progress_base_config"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginEnd="12dp"
                android:max="100"
                android:progress="0"
                app:layout_constraintBottom_toBottomOf="@+id/btn_base_config_download"
                app:layout_constraintEnd_toStartOf="@+id/btn_base_config_download"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/btn_base_config_download" />

            <TextView
                android:id="@+id/tv_model_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="@string/model_download_title"
                android:textSize="13sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btn_base_config_download" />

            <RadioGroup
                android:id="@+id/rg_voice_gender"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_model_title">

                <RadioButton
                    android:id="@+id/rb_male"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:checked="true"
                    android:text="男声" />

                <RadioButton
                    android:id="@+id/rb_female"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="女声" />

            </RadioGroup>

            <Button
                android:id="@+id/btn_model_play"
                android:layout_width="130dp"
                android:layout_height="48dp"
                android:enabled="false"
                android:text="数字人页面"
                android:textColor="@color/black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/rg_voice_gender" />

            <ProgressBar
                android:id="@+id/progress_model"
                style="@style/Widget.AppCompat.ProgressBar.Horizontal"
                android:layout_width="0dp"
                android:layout_height="48dp"
                android:layout_marginEnd="12dp"
                android:max="100"
                android:progress="0"
                app:layout_constraintBottom_toBottomOf="@+id/btn_model_play"
                app:layout_constraintEnd_toStartOf="@+id/btn_model_play"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/btn_model_play" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_tts_url"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="0dp"
                android:layout_marginTop="24dp"
                android:hint="TTS服务地址"
                app:hintTextColor="@color/black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/progress_model">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_tts_url"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textUri"
                    android:text="http://14.19.140.88:8280/v1/tts"
                    android:textSize="14sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_apikey"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="0dp"
                android:layout_marginTop="12dp"
                android:hint="API Key"
                app:hintTextColor="@color/black"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/til_tts_url">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_apikey"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 语音检测参数调整区域 -->
            <TextView
                android:id="@+id/tv_voice_detection_settings"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:text="语音检测参数设置"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/til_apikey" />

            <!-- 振幅阈值 -->
            <TextView
                android:id="@+id/tv_amplitude_threshold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="振幅阈值(越小越灵敏):"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_voice_detection_settings" />

            <TextView
                android:id="@+id/tv_amplitude_threshold_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="300"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/tv_amplitude_threshold"
                app:layout_constraintStart_toEndOf="@id/tv_amplitude_threshold"
                app:layout_constraintTop_toTopOf="@id/tv_amplitude_threshold" />

            <SeekBar
                android:id="@+id/seekBar_amplitude_threshold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:max="400"
                android:progress="200"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_amplitude_threshold" />

            <!-- 语音检测阈值 -->
            <TextView
                android:id="@+id/tv_voice_detection_threshold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="语音检测阈值(帧数):"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/seekBar_amplitude_threshold" />

            <TextView
                android:id="@+id/tv_voice_detection_threshold_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="1"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/tv_voice_detection_threshold"
                app:layout_constraintStart_toEndOf="@id/tv_voice_detection_threshold"
                app:layout_constraintTop_toTopOf="@id/tv_voice_detection_threshold" />

            <SeekBar
                android:id="@+id/seekBar_voice_detection_threshold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:max="4"
                android:progress="0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_voice_detection_threshold" />

            <!-- 静音检测时长 -->
            <TextView
                android:id="@+id/tv_silence_detection_ms"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="静音检测时长:"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/seekBar_voice_detection_threshold" />

            <TextView
                android:id="@+id/tv_silence_detection_ms_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="800ms"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/tv_silence_detection_ms"
                app:layout_constraintStart_toEndOf="@id/tv_silence_detection_ms"
                app:layout_constraintTop_toTopOf="@id/tv_silence_detection_ms" />

            <SeekBar
                android:id="@+id/seekBar_silence_detection_ms"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:max="40"
                android:progress="20"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_silence_detection_ms" />

            <!-- 有效帧比例 -->
            <TextView
                android:id="@+id/tv_valid_frame_ratio"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="有效帧比例(越小越灵敏):"
                android:textSize="14sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/seekBar_silence_detection_ms" />

            <TextView
                android:id="@+id/tv_valid_frame_ratio_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="5%"
                android:textSize="14sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/tv_valid_frame_ratio"
                app:layout_constraintStart_toEndOf="@id/tv_valid_frame_ratio"
                app:layout_constraintTop_toTopOf="@id/tv_valid_frame_ratio" />

            <SeekBar
                android:id="@+id/seekBar_valid_frame_ratio"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:max="19"
                android:progress="4"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tv_valid_frame_ratio" />

            <!-- 重置按钮 -->
            <Button
                android:id="@+id/btn_reset_voice_params"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:text="重置为默认参数"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/seekBar_valid_frame_ratio" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>